# 🤯 LobeHub 桌面应用程序

LobeHub Desktop 是 [LobeChat](https://github.com/lobehub/lobe-chat) 的跨平台桌面应用程序，使用 Electron 构建，提供了更加原生的桌面体验和功能。

## ✨ 功能特点

- **🌍 跨平台支持**：支持 macOS (Intel/Apple Silicon)、Windows 和 Linux 系统
- **🔄 自动更新**：内置更新机制，确保您始终使用最新版本
- **🌐 多语言支持**：完整的 i18n 支持，包含 18+ 种语言的懒加载
- **🎨 原生集成**：与操作系统深度集成，提供原生菜单、快捷键和通知
- **🔒 安全可靠**：macOS 公证认证，加密令牌存储，安全的 OAuth 流程
- **📦 多渠道发布**：提供稳定版、测试版和每日构建版本
- **⚡ 高级窗口管理**：多窗口架构，支持主题同步
- **🔗 远程服务器同步**：与远程 LobeChat 实例的安全数据同步
- **🎯 开发者工具**：内置开发面板和全面的调试工具

## 🚀 开发环境设置

### 前提条件

- **Node.js** 22+
- **pnpm** 10+
- **Electron** 兼容的开发环境

### 快速开始

```bash
# 安装依赖
pnpm install-isolated

# 启动开发服务器
pnpm electron:dev

# 类型检查
pnpm typecheck

# 运行测试
pnpm test
```

### 环境变量配置

复制 `.env.desktop` 到 `.env` 并根据需要配置：

```bash
cp .env.desktop .env
```

> \[!WARNING]
> 在进行更改之前请备份您的 `.env` 文件，避免丢失配置。

### 构建命令

| 命令               | 描述                               |
| ------------------ | ---------------------------------- |
| `pnpm build`       | 构建所有平台                       |
| `pnpm build:mac`   | 构建 macOS (Intel + Apple Silicon) |
| `pnpm build:win`   | 构建 Windows                       |
| `pnpm build:linux` | 构建 Linux                         |
| `pnpm build-local` | 本地开发构建                       |

### 开发工作流

```bash
# 1. 开发
pnpm electron:dev # 启动热重载开发服务器

# 2. 代码质量
pnpm lint      # ESLint 检查
pnpm format    # Prettier 格式化
pnpm typecheck # TypeScript 验证

# 3. 测试
pnpm test # 运行 Vitest 测试

# 4. 构建和打包
pnpm build       # 生产构建
pnpm build-local # 本地测试构建
```

## 🎯 发布渠道

| 渠道                     | 描述                   | 稳定性 | 自动更新 |
| ------------------------ | ---------------------- | ------ | -------- |
| **稳定版**               | 经过充分测试的正式版本 | 🟢 高  | ✅ 是    |
| **测试版 (Beta)**        | 包含新功能的预发布版本 | 🟡 中  | ✅ 是    |
| **每日构建版 (Nightly)** | 包含最新更改的每日构建 | 🟠 低  | ✅ 是    |

## 🛠 技术栈

### 核心框架

- **Electron** `37.1.0` - 跨平台桌面框架
- **Node.js** `22+` - 后端运行时
- **TypeScript** `5.7+` - 类型安全开发
- **Vite** `6.2+` - 构建工具

### 架构和模式

- **依赖注入** - 基于装饰器注册的 IoC 容器
- **事件驱动架构** - 进程间 IPC 通信
- **模块联邦** - 动态控制器和服务加载
- **观察者模式** - 状态管理和 UI 同步

### 开发工具

- **Vitest** - 单元测试框架
- **ESLint** - 代码检查
- **Prettier** - 代码格式化
- **electron-builder** - 应用程序打包
- **electron-updater** - 自动更新机制

### 安全和存储

- **Electron Safe Storage** - 加密令牌存储
- **OAuth 2.0 + PKCE** - 安全认证流程
- **electron-store** - 持久化配置
- **自定义协议处理器** - 安全回调处理

## 🏗 架构设计

桌面应用程序采用了复杂的依赖注入和事件驱动架构：

### 📁 核心结构

```
src/main/core/
├── App.ts                    # 🎯 主应用程序协调器
├── IoCContainer.ts           # 🔌 依赖注入容器
├── window/                   # 🪟 窗口管理模块
│   ├── WindowThemeManager.ts     # 🎨 主题同步
│   ├── WindowPositionManager.ts  # 📐 位置持久化
│   ├── WindowErrorHandler.ts     # ⚠️  错误边界
│   └── WindowConfigBuilder.ts    # ⚙️  配置构建器
├── browser/                  # 🌐 浏览器管理模块
│   ├── Browser.ts               # 🪟 单个窗口实例
│   └── BrowserManager.ts        # 👥 多窗口协调器
├── ui/                       # 🎨 UI 系统模块
│   ├── Tray.ts                  # 📍 系统托盘集成
│   ├── TrayManager.ts           # 🔧 托盘管理
│   ├── MenuManager.ts           # 📋 原生菜单系统
│   └── ShortcutManager.ts       # ⌨️  全局快捷键
└── infrastructure/           # 🔧 基础设施服务
    ├── StoreManager.ts          # 💾 配置存储
    ├── I18nManager.ts           # 🌍 国际化
    ├── UpdaterManager.ts        # 📦 自动更新系统
    └── StaticFileServerManager.ts # 🗂️ 本地文件服务
```

### 🔄 应用程序生命周期

`App.ts` 类通过几个关键阶段协调整个应用程序的生命周期：

#### 1. 🚀 初始化阶段

- **系统信息记录** - 捕获操作系统、CPU、内存和区域设置详细信息
- **存储管理器设置** - 初始化持久配置存储
- **动态模块加载** - 通过 glob 导入自动发现控制器和服务
- **IPC 事件注册** - 设置进程间通信通道

#### 2. 🏃 引导阶段

- **单实例检查** - 确保只运行一个应用程序实例
- **IPC 服务器启动** - 启动通信服务器
- **核心管理器初始化** - 按顺序初始化所有管理器：
  - 🌍 国际化管理器
  - 📋 原生菜单系统
  - 🗂️ 本地资源服务器
  - ⌨️ 全局快捷键注册
  - 🪟 浏览器窗口管理
  - 📍 系统托盘（仅 Windows）
  - 📦 自动更新系统

### 🔧 核心组件深度解析

#### 🌐 浏览器管理系统

- **多窗口架构** - 支持聊天、设置和开发工具窗口
- **窗口状态管理** - 处理定位、主题和生命周期
- **WebContents 映射** - WebContents 和标识符之间的双向映射
- **事件广播** - 向所有或特定窗口的集中事件分发

#### 🔌 依赖注入和事件系统

- **IoC 容器** - 基于 WeakMap 的装饰控制器方法容器
- **装饰器注册** - `@ipcClientEvent` 和 `@ipcServerEvent` 装饰器
- **自动事件映射** - 控制器加载期间注册的事件
- **服务定位器** - 类型安全的服务和控制器检索

#### 🪟 窗口管理

- **主题感知窗口** - 自动适应系统深色 / 浅色模式
- **平台特定样式** - Windows 标题栏和覆盖自定义
- **位置持久化** - 跨会话保存和恢复窗口位置
- **错误边界** - 窗口操作的集中错误处理

#### 🔧 基础设施服务

##### 🌍 国际化管理器

- **18+ 语言支持** 懒加载和命名空间组织
- **系统集成** 与 Electron 的区域检测集成
- **动态 UI 刷新** 语言更改时的 UI 更新
- **资源管理** 高效的加载策略

##### 📦 更新管理器

- **多渠道支持** （稳定版、测试版、每日构建）可配置间隔
- **后台下载** 进度跟踪和用户通知
- **回滚保护** 错误处理和恢复机制
- **渠道管理** 自动渠道切换

##### 💾 存储管理器

- **类型安全存储** 使用带有 TypeScript 接口的 electron-store
- **加密机密** 通过 Electron 的安全存储 API
- **配置验证** 默认值管理
- **文件系统集成** 自动目录创建

##### 🗂️ 静态文件服务器

- **本地 HTTP 服务器** 用于提供应用程序资源和用户文件
- **安全控制** 请求过滤和访问验证
- **文件管理** 上传、下载和删除功能
- **路径解析** 存储位置之间的智能路由

#### 🎨 UI 系统集成

- **全局快捷键** - 平台感知的键盘快捷键注册与冲突检测
- **系统托盘** - 带有上下文菜单和通知的原生集成
- **原生菜单** - 带有 i18n 的平台特定应用程序和上下文菜单
- **主题同步** - 所有 UI 组件的自动主题更新

### 🏛 控制器和服务架构

#### 🎮 控制器模式

- **IPC 事件处理** - 通过基于装饰器的注册处理来自渲染器的事件
- **生命周期钩子** - 初始化阶段的 `beforeAppReady` 和 `afterAppReady`
- **类型安全通信** - 所有 IPC 事件和响应的强类型
- **错误边界** - 具有适当传播的全面错误处理

#### 🔧 服务模式

- **业务逻辑封装** - 关注点的清晰分离
- **依赖管理** - 通过 IoC 容器管理
- **跨控制器共享** - 通过服务定位器模式访问的服务
- **资源管理** - 适当的初始化和清理

### 🔗 进程间通信

#### 📡 IPC 系统功能

- **双向通信** - Main↔Renderer 和 Main↔Next.js 服务器
- **类型安全事件** - 所有事件参数的 TypeScript 接口
- **上下文感知** - 事件包含用于窗口特定操作的发送者上下文
- **错误传播** - 具有适当状态码的集中错误处理

#### 🛡️ 安全功能

- **OAuth 2.0 + PKCE** - 具有状态参数验证的安全认证
- **加密令牌存储** - 在可用时使用 Electron 的安全存储 API
- **自定义协议处理器** - OAuth 流程的安全回调处理
- **请求过滤** - 网络请求和外部链接的安全控制

## 🧪 测试

### 测试结构

```bash
apps/desktop/src/main/controllers/__tests__/ # 控制器单元测试
tests/                                       # 集成测试
```

### 运行测试

```bash
pnpm test       # 运行所有测试
pnpm test:watch # 监视模式
pnpm typecheck  # 类型验证
```

### 测试覆盖

- **控制器测试** - IPC 事件处理验证
- **服务测试** - 业务逻辑验证
- **集成测试** - 端到端工作流测试
- **类型测试** - TypeScript 接口验证

## 🔒 安全功能

### 认证和授权

- **OAuth 2.0 流程** 使用 PKCE 进行安全令牌交换
- **状态参数验证** 防止 CSRF 攻击
- **加密令牌存储** 使用平台原生安全存储
- **自动令牌刷新** 在失败时回退到重新认证

### 应用程序安全

- **代码签名** - macOS 公证认证以增强安全性
- **沙盒** - 对系统资源的受控访问
- **CSP 控制** - 内容安全策略管理
- **请求过滤** - 外部请求的安全控制

### 数据保护

- **加密配置** - 敏感数据静态加密
- **安全 IPC** - 类型安全的通信通道
- **路径验证** - 安全的文件系统访问控制
- **网络安全** - HTTPS 强制和代理支持

## 🤝 参与贡献

桌面应用程序开发涉及复杂的跨平台考虑和原生集成。我们欢迎社区贡献来改进功能、性能和用户体验。您可以通过以下方式参与改进：

### 如何贡献

1. **平台支持**：增强跨平台兼容性和原生集成
2. **性能优化**：改进应用程序启动时间、内存使用和响应性
3. **功能开发**：添加新的桌面特定功能和能力
4. **错误修复**：修复平台特定问题和边缘情况
5. **安全改进**：增强安全措施和认证流程
6. **UI/UX 增强**：改进桌面用户界面和体验

### 贡献流程

1. Fork [LobeChat 仓库](https://github.com/lobehub/lobe-chat)
2. 按照我们的设置指南建立桌面开发环境
3. 对桌面应用程序进行修改
4. 提交 Pull Request 并描述：

- 平台兼容性测试结果
- 性能影响分析
- 安全考虑
- 用户体验改进
- 破坏性更改（如有）

### 开发领域

- **核心架构**：依赖注入、事件系统和生命周期管理
- **窗口管理**：多窗口支持、主题同步和状态持久化
- **IPC 通信**：主进程和渲染进程之间的类型安全进程间通信
- **平台集成**：原生菜单、快捷键、通知和系统托盘
- **安全功能**：OAuth 流程、令牌加密和安全存储
- **自动更新系统**：多渠道更新和回滚机制

## 📚 其他资源

- **开发指南**：[`Development.md`](./Development.md) - 全面的开发文档
- **架构文档**：[`/docs`](../../docs/) - 详细的技术规范
- **贡献指南**：[`CONTRIBUTING.md`](../../CONTRIBUTING.md) - 贡献指导
- **问题和支持**：[GitHub Issues](https://github.com/lobehub/lobe-chat/issues)
