---
title: Using Taichu API Key in LobeChat
description: >-
  Learn how to integrate Taichu AI into LobeChat for enhanced conversational experiences. Follow the steps to configure Taichu AI and start using its models.

tags:
  - LobeChat
  - Taichu
  - API Key
  - Web UI
---

# Using Taichu in LobeChat

<Image alt={'Using Taichu in LobeChat'} cover src={'https://github.com/user-attachments/assets/9cb27b68-f2ac-4ff9-8f97-d96314b1af03'} />

This article will guide you on how to use Taichu in LobeChat:

<Steps>
  ### Step 1: Obtain Taichu API Key

  - Create an account on [Taichu](https://ai-maas.wair.ac.cn/)
  - Create and obtain an [API key](https://ai-maas.wair.ac.cn/#/settlement/api/key)

  <Image alt={'Create API Key'} inStep src={'https://github.com/user-attachments/assets/8d90ae64-cf8e-4d90-8a31-c18ab484740b'} />

  ### Step 2: Configure Taichu in LobeChat

  - Go to the `Settings` interface in LobeChat
  - Find the setting for `Taichu` under `AI Service Provider`

  <Image alt={'Enter API Key'} inStep src={'https://github.com/user-attachments/assets/55028fe5-44db-49e2-93c5-5dabbd664f10'} />

  - Enter the obtained API key
  - Choose a Purple Taichu model for your AI assistant to start the conversation

  <Image alt={'Select Tai Chi model and start conversation'} inStep src={'https://github.com/user-attachments/assets/c44b6894-70cb-4876-b792-2e76e75ac542'} />

  <Callout type={'warning'}>
    During usage, you may need to pay the API service provider, please refer to Taichu's relevant
    pricing policies.
  </Callout>
</Steps>

Now you can start conversing with the models provided by Taichu in LobeChat.
