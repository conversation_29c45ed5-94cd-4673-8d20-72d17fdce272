---
title: Using Zhipu ChatGLM API Key in LobeChat
description: >-
  Learn how to integrate and utilize Zhipu AI models in LobeChat for enhanced conversational experiences. Obtain the API key, configure settings, and start engaging with cognitive intelligence.

tags:
  - Zhipu AI
  - ChatGLM
  - API Key
  - Web UI
---

# Using Zhipu ChatGLM in LobeChat

<Image alt={'Using Together AI in LobeChat'} cover src={'https://github.com/lobehub/lobe-chat/assets/34400653/497e3b20-57ca-4963-b6f4-897c9710c16e'} />

[Zhipu AI](https://www.zhipuai.cn/) is a high-tech company originating from the Department of Computer Science at Tsinghua University. Established in 2019, the company focuses on natural language processing, machine learning, and big data analysis, dedicated to expanding the boundaries of artificial intelligence technology in the field of cognitive intelligence.

This document will guide you on how to use Zhipu AI in LobeChat:

<Steps>
  ### Step 1: Obtain the API Key for Zhipu AI

  - Visit and log in to the [Zhipu AI Open Platform](https://open.bigmodel.cn/)
  - Upon initial login, the system will automatically create an API key for you and gift you a resource package of 25M Tokens
  - Navigate to the `API Key` section at the top to view your API key

  <Image alt={'Obtaining Zhipu AI API Key'} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/6d6f2bc5-1407-471d-95a8-fb03193edbdb'} />

  ### Step 2: Configure Zhipu AI in LobeChat

  - Visit the `Settings` interface in LobeChat
  - Under `AI Service Provider`, locate the settings for Zhipu AI

  <Image alt={'Enter Zhipu AI API Key in LobeChat'} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/2afffe79-1d37-423c-9363-f09605d5e640'} />

  - Open Zhipu AI and enter the obtained API key
  - Choose a Zhipu AI model for your assistant to start the conversation

  <Image alt={'Select and use Zhipu AI model'} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/b83da559-73d1-4734-87d5-5e22955a9da2'} />

  <Callout type={'warning'}>
    During usage, you may need to pay the API service provider, please refer to Zhipu AI's pricing
    policy.
  </Callout>
</Steps>

You can now engage in conversations using the models provided by Zhipu AI in LobeChat.
