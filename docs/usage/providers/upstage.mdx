---
title: Using Upstage in LobeChat
description: Learn how to integrate and utilize Upstage's language model APIs in LobeChat.
tags:
  - LobeChat
  - Upstage
  - API Key
  - Web UI
---

# Using Upstage in LobeChat

<Image cover src={'https://github.com/user-attachments/assets/14696698-03f7-4856-b36c-9a53997eb12c'} />

[Upstage](https://www.upstage.ai/) is a platform that offers AI models and services, focusing on applications in natural language processing and machine learning. It allows developers to access its powerful AI capabilities through APIs, supporting various tasks such as text generation and conversational systems.

This article will guide you on how to use Upstage in LobeChat.

<Steps>
  ### Step 1: Obtain an Upstage API Key

  - Register and log in to the [Upstage Console](https://console.upstage.ai/home)
  - Navigate to the `API Keys` page
  - Create a new API key
  - Copy and save the generated API key

  <Image alt={'Save the API Key'} inStep src={'https://github.com/user-attachments/assets/8a0225e0-16ed-40ce-9cd5-553dda561679'} />

  ### Step 2: Configure Upstage in LobeChat

  - Access the `Settings` interface in LobeChat
  - Locate the `Upstage` settings under `AI Service Provider`

  <Image alt={'Enter API Key'} inStep src={'https://github.com/user-attachments/assets/e89d2a56-4bf0-4bff-ac39-0d44789fa858'} />

  - Enter the obtained API key
  - Select an Upstage model for your AI assistant to start the conversation

  <Image alt={'Select Upstage Model and Start Conversation'} inStep src={'https://github.com/user-attachments/assets/88e14294-20a6-47c6-981e-fb65453b57cd'} />

  <Callout type={'warning'}>
    Please note that you may need to pay the API service provider for usage. Refer to Upstage's
    pricing policy for more information.
  </Callout>
</Steps>

You can now use the models provided by Upstage for conversations in LobeChat.
