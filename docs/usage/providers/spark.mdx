---
title: Using iFLYTEK Spark in LobeChat
description: Learn how to integrate and utilize iFLYTEK's Spark model APIs in LobeChat.
tags:
  - LobeChat
  - iFLYTEK
  - Spark
  - API Key
  - Web UI
---

# Using iFLYTEK Spark in LobeChat

<Image cover src={'https://github.com/user-attachments/assets/f3068287-8ade-4eca-9841-ea67d8ff1226'} />

[iFLYTEK Spark](https://xinghuo.xfyun.cn/) is a powerful AI model launched by iFLYTEK, equipped with cross-domain knowledge and language understanding capabilities, able to perform various tasks such as Q\&A, conversations, and literary creation.

This guide will instruct you on how to use iFLYTEK Spark in LobeChat.

<Steps>
  ### Step 1: Obtain the iFLYTEK Spark API Key

  - Register and log in to the [iFLYTEK Open Platform](https://console.xfyun.cn/)
  - Create an application

  <Image alt={'Create Application'} inStep src={'https://github.com/user-attachments/assets/1bf1a5f0-32ad-418c-a8d1-6c54740f50b9'} />

  - Select a large model to view details
  - Copy the `API Password` from the top right corner under the HTTP service interface authentication information

  <Image alt={'Copy API Key'} inStep src={'https://github.com/user-attachments/assets/7239d611-1989-414b-a51c-444e47096d75'} />

  ### Step 2: Configure iFLYTEK Spark in LobeChat

  - Access the `Settings` menu in LobeChat
  - Find the iFLYTEK Spark settings under `AI Service Provider`

  <Image alt={'Enter API Key'} inStep src={'https://github.com/user-attachments/assets/d693be02-e08c-43ae-8bde-1294f180aaf6'} />

  - Input the obtained API Key
  - Choose an iFLYTEK Spark model for your AI assistant to start the conversation

  <Image alt={'Select iFLYTEK Spark Model and Start Conversation'} inStep src={'https://github.com/user-attachments/assets/8910186f-4609-4798-a588-2780dcf8db60'} />

  <Callout type={'warning'}>
    During usage, you may need to pay the API service provider, please refer to the relevant pricing
    policy of iFLYTEK Spark.
  </Callout>
</Steps>

Now you can use the models provided by iFLYTEK Spark for conversations in LobeChat.
