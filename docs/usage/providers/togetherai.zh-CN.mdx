---
title: 在 LobeChat 中使用 Together AI API Key
description: 学习如何在 LobeChat 中配置和使用 Together AI 的API Key，以便开始对话和交互。
tags:
  - LobeChat
  - Together AI
  - API密钥
  - Web UI
---

# 在 LobeChat 中使用 Together AI

<Image alt={'在 LobeChat 中使用 Together AI'} cover src={'https://github.com/lobehub/lobe-chat/assets/34400653/28e025dd-367b-4add-85b6-499f4aacda61'} />

[together.ai](https://www.together.ai/) 是一家专注于生成式人工智能 (AIGC) 领域的平台，成立于 2022 年 6 月。 它致力于构建用于运行、训练和微调开源模型的云平台，以低于主流供应商的价格提供可扩展的计算能力。

本文档将指导你如何在 LobeChat 中使用 Together AI:

<Steps>
  ### 步骤一：获取 Together AI 的 API 密钥

  - 访问并登录 [Together AI API](https://api.together.ai/)
  - 初次登录时系统会自动为你创建好 API 密钥并赠送 $5.0 的额度

  <Image alt={'获得 together.ai API 密钥'} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/fcdcda9b-8668-4eac-b5cb-04803a888e92'} />

  - 如果你没有保存，也可以在后续任意时间，通过 `设置` 中的 `API 密钥` 界面查看

  <Image alt={'查看 together.ai API 密钥'} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/663335d0-fb37-4882-9c7f-ebbd53275644'} />

  ### 步骤二：在 LobeChat 中配置 Together AI

  - 访问 LobeChat 的`设置`界面
  - 在`AI 服务商`下找到`together.ai`的设置项

  <Image alt={'LobeChat 中填写 Together AI API 密钥'} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/0cc6c9b8-4688-472b-a80f-f84c5ebbc719'} />

  - 打开 together.ai 并填入获得的 API 密钥
  - 为你的助手选择一个 Together AI 的模型即可开始对话

  <Image alt={' 选择并使用 Together AI 模型 '} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/34d92da4-783f-4f16-8c4a-9d8e9a03c8da'} />

  <Callout type={'warning'}>
    在使用过程中你可能需要向 API 服务提供商付费，请参考 Together AI 的费用政策。
  </Callout>
</Steps>

至此你已经可以在 LobeChat 中使用 Together AI 提供的模型进行对话了。
