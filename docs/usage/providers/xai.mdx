---
title: Using xA<PERSON> in LobeChat
description: >-
  Learn how to configure and use xAI's API Key in LobeChat to start conversations and interactions.

tags:
  - LobeChat
  - xAI
  - API Key
  - Web UI
---

# Using xAI in LobeChat

<Image cover src={'https://github.com/user-attachments/assets/cf3bfd44-9c13-4026-95cd-67f54f40ce6c'} />

[xAI](https://x.ai/) is an artificial intelligence company founded by <PERSON><PERSON> in 2023, aimed at exploring and understanding the true nature of the universe. The company's mission is to solve complex scientific and mathematical problems using AI technology and to advance the field of artificial intelligence.

This article will guide you on how to use xAI in LobeChat.

<Steps>
  ### Step 1: Obtain an API Key from xAI

  - Register and login to the [xAI console](https://console.x.ai/)
  - Create an API token
  - Copy and save the API token

  <Image alt={'xAI API'} inStep src={'https://github.com/user-attachments/assets/09c994cf-78f8-46ea-9fef-a06022c0f6d7'} />

  <Callout type={'warning'}>
    Make sure to securely save the API token displayed in the popup; it only appears once. If you
    accidentally lose it, you will need to create a new API token.
  </Callout>

  ### Step 2: Configure xAI in LobeChat

  - Go to the `Settings` menu in LobeChat
  - Locate the `xAI` settings under `AI Service Provider`

  <Image alt={'Enter API Key'} inStep src={'https://github.com/user-attachments/assets/12863a0e-a1ee-406d-8dee-011b20701fd6'} />

  - Enter the API key you obtained
  - Select an xAI model for your AI assistant to start a conversation

  <Image alt={'Select xAI Model and Start Conversation'} inStep src={'https://github.com/user-attachments/assets/899a4393-db41-45a6-97ec-9813e1f9879d'} />

  <Callout type={'warning'}>
    During use, you may need to pay the API service provider, so please refer to xAI's relevant
    pricing policies.
  </Callout>
</Steps>

You are now ready to engage in conversations using the models provided by xAI in LobeChat.
