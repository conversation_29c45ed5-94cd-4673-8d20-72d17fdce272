---
title: Using Stepfun API Key in LobeChat
description: >-
  Learn how to integrate Stepfun AI models into LobeChat for engaging conversations. Obtain Stepfun API key, configure Stepfun in LobeChat settings, and select a model to start chatting.

tags:
  - Stepfun
  - API key
  - Web UI
---

# Using Stepfun in LobeChat

<Image alt={'Using Stepfun in LobeChat'} cover src={'https://github.com/lobehub/lobe-chat/assets/34400653/95717e2b-1a55-4fca-a96b-b1c186ed4563'} />

[Stepfun](https://www.stepfun.com/) is a startup focusing on the research and development of Artificial General Intelligence (AGI). They have released the Step-1 billion-parameter language model, Step-1V billion-parameter multimodal model, and the Step-2 trillion-parameter MoE language model preview.

This document will guide you on how to use Stepfun in LobeChat:

<Steps>
  ### Step 1: Obtain Stepfun API Key

  - Visit and log in to the [Stepfun Open Platform](https://platform.stepfun.com/)
  - Go to the `API Key` menu, where the system has already created an API key for you
  - Copy the created API key

  <Image alt={'Obtain Stepfun API Key'} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/33d8ce3b-0083-48aa-9a66-3825e726c4de'} />

  ### Step 2: Configure Stepfun in LobeChat

  - Visit the `Settings` interface in LobeChat
  - Find the setting for Stepfun under `AI Service Provider`

  <Image alt={'Enter API Key'} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/470e5669-650b-46cf-8024-a1476c166059'} />

  - Open Stepfun and enter the obtained API key
  - Choose a Stepfun model for your AI assistant to start the conversation

  <Image alt={'Select Stepfun model and start conversation'} inStep src={'https://github.com/lobehub/lobe-chat/assets/34400653/0275a552-f189-42b5-bf40-f9891c428b3d'} />

  <Callout type={'warning'}>
    During usage, you may need to pay the API service provider, please refer to Stepfun's relevant
    pricing policies.
  </Callout>
</Steps>

You can now use the models provided by Stepfun to have conversations in LobeChat.
