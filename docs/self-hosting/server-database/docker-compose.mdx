---
title: Deploying LobeChat with Docker Compose
description: >-
  Learn how to deploy the LobeChat service using Docker Compose, including configuration tutorials for various services.

tags:
  - Docker Compose
  - LobeChat
  - Docker Container
  - Deployment Guide
---

# Deploying LobeChat Server Database Version with Docker Compose

<div style={{display:"flex", gap: 4}}>
  [![][docker-release-shield]][docker-release-link]

  [![][docker-size-shield]][docker-size-link]

  [![][docker-pulls-shield]][docker-pulls-link]
</div>

<Callout type="warning">
  **Note on Docker Deployment Limitations**
  The Docker and Docker Compose deployment options do not support injecting the `NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY` through environment variables, which prevents enabling the Clerk authentication service. Recommended alternatives include:

  - Hosting deployment via Vercel

  - Running a local image build process
</Callout>

## Quick Start

<Callout type="info">
  **System Compatibility Notes**

  - One-click deployment is supported in Unix environments (Linux/macOS).

  - Windows users must run through [WSL 2](https://aka.ms/wsl).

  - The one-click startup script is only for initial deployment; for subsequent deployments, please refer to the [Custom Deployment](#custom-deployment) section.

  - Port occupation check: Ensure that ports `3210`, `8000`, `9000`, and `9001` are available.
</Callout>

Execute the following commands to set up the deployment environment; the directory `lobe-chat-db` will be used to store your configuration files and subsequent database files.

```sh
mkdir lobe-chat-db && cd lobe-chat-db
```

Fetch and execute the deployment script:

```sh
bash <(curl -fsSL https://lobe.li/setup.sh) -l en
```

The script supports the following deployment modes; please choose the appropriate mode based on your needs and read the rest of the documentation.

- [Local Mode (default)](#local-mode): Accessible only locally, not supporting LAN/public access; suitable for initial experiences.
- [Port Mode](#port-mode): Supports LAN/public `http` access; suitable for no domain or private network use.
- [Domain Mode](#domain-mode): Supports LAN/public `http/https` access with reverse proxy; suitable for personal or team use.

<Callout type="info">
  In the script's options prompt `(Option1/Option2)[Option1]`: `(Option1 / Option2)` indicates
  selectable options, while `[Option1]` indicates the default option; simply press enter to choose
  the default.
</Callout>

### Local Mode

<Steps>
  ### Complete Remaining Configuration in Interactive Script

  Continue pressing enter to use the default configuration.

  ### Check Configuration Generation Report

  After the script finishes running, you need to check the configuration generation report, which includes the accounts and initial login passwords for the Casdoor administrator and user.

  <Callout type="warning">
    Please log in to LobeChat using the user account; the administrator account is only for managing
    Casdoor.
  </Callout>

  ```log
  The results of the secure key generation are as follows:
  LobeChat:
    - URL: http://localhost:3210
    - Username: user
    - Password: c66f8c
  Casdoor:
    - URL: http://localhost:8000
    - Username: admin
    - Password: c66f8c
  Minio:
    - URL: http://localhost:9000
    - Username: admin
    - Password: 8c82ea41
  ```

  ### Start Docker

  ```sh
  docker compose up -d
  ```

  ### Check Logs

  ```sh
  docker logs -f lobe-chat
  ```

  If you see the following logs in the container, it means the startup was successful:

  ```log
  [Database] Start to migration...
  ✅ database migration pass.
  -------------------------------------
    ▲ Next.js 14.x.x
    - Local:        http://localhost:3210
    - Network:      http://0.0.0.0:3210

   ✓ Starting...
   ✓ Ready in 95ms
  ```

  ### Access Application

  Visit your LobeChat service at [http://localhost:3210](http://localhost:3210). The account credentials for the application can be found in the report from step `2`.
</Steps>

### Port Mode

<Steps>
  ### Complete Remaining Configuration in Interactive Script

  In port mode, you need to complete the following based on the script prompts:

  - Server IP address settings: for LAN/public access.
  - Regenerate secure keys: We highly recommend regenerating the secure keys; if you lack the key generation library required by the script, we suggest referring to the [Custom Deployment](#custom-deployment) section for key modifications.

  ### Check Configuration Generation Report

  After the script finishes running, please check the configuration generation report for the Casdoor administrator account, user account, and their initial login passwords.

  <Callout type="warning">
    Please log in to LobeChat using the user account; the administrator account is only for managing
    Casdoor.
  </Callout>

  ```log
  The results of the secure key generation are as follows:
  LobeChat:
    - URL: http://your_server_ip:3210
    - Username: user
    - Password: 837e26
  Casdoor:
    - URL: http://your_server_ip:8000
    - Username: admin
    - Password: 837e26
  Minio:
    - URL: http://your_server_ip:9000
    - Username: admin
    - Password: dbac8440
  ```

  ### Start Docker

  ```sh
  docker compose up -d
  ```

  ### Check Logs

  ```sh
  docker logs -f lobe-chat
  ```

  If you see the following logs in the container, it means the startup was successful:

  ```log
  [Database] Start to migration...
  ✅ database migration pass.
  -------------------------------------
    ▲ Next.js 14.x.x
    - Local:        http://your_server_ip:3210
    - Network:      http://0.0.0.0:3210
   ✓ Starting...
   ✓ Ready in 95ms
  ```

  ### Access Application

  You can access your LobeChat service at `http://your_server_ip:3210`. The account credentials for the application can be found in the report from step `2`.

  <Callout type="warning">
    If your service can accessed via the public network,
    we strongly recommend disabling the registration,
    refer to the [documentation](https://lobehub.com/docs/self-hosting/advanced/auth/next-auth/casdoor)
    for more information.
  </Callout>
</Steps>

### Domain Mode

<Steps>
  ### Complete Reverse Proxy Configuration

  In domain mode, you need to complete the reverse proxy configuration and ensure that the LAN/public can access the following services. Please use a reverse proxy to map the following service ports to the domain names:

  | Domain                 | Proxy Port | Required |
  | ---------------------- | ---------- | -------- |
  | `lobe.example.com`     | `3210`     | Yes      |
  | `auth.example.com`     | `8000`     | Yes      |
  | `minio.example.com`    | `9000`     | Yes      |
  | `minio-ui.example.com` | `9001`     |          |

  <Callout type="important">
    If you are using panel software like [aaPanel](https://www.bt.cn/) for reverse proxy configuration,
    ensure it does not intercept requests to the `.well-known` path to facilitate the proper functioning of Casdoor's OAuth2 configuration.
    Below is a whitelist configuration for the Nginx server block concerning paths for Casdoor reverse proxy:

    ```nginx
    location /.well-known/openid-configuration {
    proxy_pass http://localhost:8000;  # Forward to localhost:8000
    proxy_set_header Host $host;  # Keep the original host header
    proxy_set_header X-Real-IP $remote_addr;  # Keep the client's real IP
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;  # Keep the forwarded IP
    proxy_set_header X-Forwarded-Proto $scheme;  # Keep the request protocol
    }
    ```

    ⚠️ If you are using such panel software,
    please do not enable any form of caching in the reverse proxy settings of such panel software to avoid affecting the normal operation of the service.
    Read more at [https://github.com/lobehub/lobe-chat/discussions/5986](https://github.com/lobehub/lobe-chat/discussions/5986)
  </Callout>

  ### Complete Remaining Configuration in Interactive Script

  In domain mode, you need to complete the following configurations based on script prompts:

  - Domain setup for the LobeChat service: `lobe.example.com`
  - Domain setup for the Minio service: `minio.example.com`
  - Domain setup for the Casdoor service: `auth.example.com`
  - Choose the access protocol: `http` or `https`
  - Regenerate secure keys: We highly recommend regenerating the secure keys; if you lack the key generation library required by the script, we suggest referring to the [Custom Deployment](#custom-deployment) section for key modifications.

    <Callout type="warning">
      The following issues may impede access to your service:

      - The domain configuration here must match the reverse proxy configuration in step `1`.

      - If you are using Cloudflare for domain resolution and have activated `full proxy`, please use the `https` protocol.

      - If you have used the HTTPS protocol, ensure that your domain certificate is correctly configured; one-click deployment does not support self-signed certificates by default.
    </Callout>

  ### Check Configuration Generation Report

  After the script finishes running, you need to check the configuration generation report, which includes the initial login password for the Casdoor administrator.

  <Callout type="warning">
    Please log in to LobeChat using the user account; the administrator account is only for managing
    Casdoor.
  </Callout>

  ```log
  The results of the secure key generation are as follows:
  LobeChat:
    - URL: https://lobe.example.com
    - Username: user
    - Password: 837e26
  Casdoor:
    - URL: https://auth.example.com
    - Username: admin
    - Password: 837e26
  Minio:
    - URL: https://minio.example.com
    - Username: admin
    - Password: dbac8440
  ```

  ### Start Docker

  ```sh
  docker compose up -d
  ```

  ### Check Logs

  ```sh
  docker logs -f lobe-chat
  ```

  If you see the following logs in the container, it indicates a successful startup:

  ```log
  [Database] Start to migration...
  ✅ database migration pass.
  -------------------------------------
    ▲ Next.js 14.x.x
    - Local:        https://localhost:3210
    - Network:      http://0.0.0.0:3210
    ✓ Starting...
    ✓ Ready in 95ms
  ```

  ### Access Application

  You can access your LobeChat service via `https://lobe.example.com`. The account credentials for the application can be found in the report from step `3`.

  <Callout type="warning">
    If your service can accessed via the public network,
    we strongly recommend disabling the registration,
    refer to the [documentation](https://lobehub.com/docs/self-hosting/advanced/auth/next-auth/casdoor)
    for more information.
  </Callout>
</Steps>

## Custom Deployment

This section mainly introduces the configurations that need to be modified to customize the deployment of the LobeChat service in different network environments. Before starting, you can download the [Docker Compose configuration file](https://raw.githubusercontent.com/lobehub/lobe-chat/HEAD/docker-compose/local/docker-compose.yml) and the [environment variable configuration file](https://raw.githubusercontent.com/lobehub/lobe-chat/refs/heads/main/docker-compose/local/.env.example).

```sh
curl -O https://raw.githubusercontent.com/lobehub/lobe-chat/HEAD/docker-compose/local/docker-compose.yml
curl -O https://raw.githubusercontent.com/lobehub/lobe-chat/HEAD/docker-compose/local/.env.example
mv .env.example .env
```

<Callout type="info">
  This section does not cover all complete variables; remaining variables can be referenced in
  [Deploying with the Server Database](/en/docs/self-hosting/server-database).
</Callout>

### Prerequisites

Generally, to fully run the LobeChat database version, you will need at least the following four services:

- The LobeChat database version itself
- PostgreSQL database with PGVector plugin
- Object storage service that supports S3 protocol
- An SSO authentication service supported by LobeChat

These services can be combined through self-hosting or online cloud services to meet various deployment needs. In this article, we provide a Docker Compose configuration entirely based on open-source self-hosted services, which can be used directly to start the LobeChat database version or modified to suit your requirements.

We use [MinIO](https://github.com/minio/minio) as the local S3 object storage service and [Casdoor](https://github.com/casdoor/casdoor) as the local authentication service by default.

<Callout type="warning">
  If your network topology is complex, please make sure these services can communicate properly
  within your network environment.
</Callout>

### Necessary Configuration

Now, we will introduce the necessary configurations for running these services:

1. Casdoor

- LobeChat requires communication with Casdoor, so you need to configure Casdoor's Issuer.

```env
AUTH_CASDOOR_ISSUER=https://auth.example.com
```

This configuration will affect LobeChat's login authentication service, and you need to ensure that the URL of the Casdoor service is correct. You can find common manifestations and solutions for errors in this configuration in the [FAQ](#faq).

- Additionally, you need to allow the callback URL in Casdoor to point to the LobeChat address:

Please add a line in the `Authentication -> Application` -> `<Application ID, default is app-built-in>` -> `Redirect URI` in Casdoor's web panel:

```
https://auth.example.com/api/auth/callback/casdoor
```

- Casdoor needs to provide the Origin information for access in the environment variables:

```env
origin=https://auth.example.com
```

2. MinIO

- LobeChat needs to provide a public access URL for object files for the LLM service provider, hence you need to configure MinIO's Endpoint.

```env
S3_PUBLIC_DOMAIN=https://minio.example.com
S3_ENDPOINT=https://minio.example.com
```

3. PostgreSQL

This configuration is found in the `docker-compose.yml` file, and you will need to configure the database name and password:

```yaml
services:
  lobe:
    environment:
      - 'DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD}@postgresql:5432/${LOBE_DB_NAME}'
```

## FAQ

#### Unable to Log In Properly

Check for the following errors based on the container logs:

```sh
docker logs -f lobe-chat
```

- r3: "response" is not a conform Authorization Server Metadata response (unexpected HTTP status code)

```log
lobe-chat      | [auth][error] r3: "response" is not a conform Authorization Server Metadata response (unexpected HTTP status code)
```

Cause: This issue is typically caused by improper reverse proxy configuration; you need to ensure your reverse proxy configuration does not intercept the Casdoor OAuth2 configuration requests.

Solutions:

- Please refer to the reverse proxy configuration notes in the [Domain Mode](#domain-mode) section.

- A direct troubleshooting method is to access `https://auth.example.com/.well-known/openid-configuration` directly; if:

  - Non-JSON format data is returned, it indicates your reverse proxy configuration is incorrect.
  - If the returned JSON format data contains an `"issuer": "URL"` field that does not match your configured `https://auth.example.com`, it indicates your environment variable configuration is incorrect.

- TypeError: fetch failed

```log
lobe-chat      | [auth][error] TypeError: fetch failed
```

Cause: LobeChat cannot access the authentication service.

Solutions:

- Check whether your authentication service is running properly and whether LobeChat's network can reach the authentication service.

- A straightforward troubleshooting method is to use the `curl` command in the LobeChat container terminal to access your authentication service at `https://auth.example.com/.well-known/openid-configuration`. If JSON format data is returned, it indicates your authentication service is functioning correctly.

````markdown
## Extended Configuration

To enhance your LobeChat service, you can perform the following extended configurations according to your needs.

### Use MinIO to Store Casdoor Avatars

Allow users to change their avatars in Casdoor.

1. First, create a bucket named `casdoor` in `buckets`, select a custom policy, and copy and paste the content below (if you modify the bucket name, please find and replace accordingly).

   ```json
   {
     "Statement": [
       {
         "Effect": "Allow",
         "Principal": {
           "AWS": ["*"]
         },
         "Action": ["s3:GetBucketLocation"],
         "Resource": ["arn:aws:s3:::casdoor"]
       },
       {
         "Effect": "Allow",
         "Principal": {
           "AWS": ["*"]
         },
         "Action": ["s3:ListBucket"],
         "Resource": ["arn:aws:s3:::casdoor"],
         "Condition": {
           "StringEquals": {
             "s3:prefix": ["files/*"]
           }
         }
       },
       {
         "Effect": "Allow",
         "Principal": {
           "AWS": ["*"]
         },
         "Action": ["s3:PutObject", "s3:DeleteObject", "s3:GetObject"],
         "Resource": ["arn:aws:s3:::casdoor/**"]
       }
     ],
     "Version": "2012-10-17"
   }
   ```
````

2. Create a new access key and store the generated `Access Key` and `Secret Key`.

3. In Casdoor's `Authentication -> Providers`, associate the MinIO S3 service. Below is an example configuration:

   ![casdoor](https://github.com/user-attachments/assets/71035610-0706-434e-9488-ab5819b55330)

   Here, the client ID and client secret correspond to the `Access Key` and `Secret Key` from the previous step; replace `**************` with `your_server_ip`.

4. In Casdoor's `Authentication -> Apps`, add a provider to the `app-built-in` application, select `minio`, and save and exit.

5. You can attempt to upload a file in Casdoor's `Authentication -> Resources` to test if the configuration is correct.

### Migrating from `logto` to `Casdoor` in Production Deployment

This is applicable for users who have been using `logto` as their login and authentication service in a production environment.

<Callout type="info">
  Due to significant instability when using [Logto](https://logto.io/) as a login and authentication
  service, the following tutorial is based on deploying with an IP mode, implementing a domain
  release solution using Casdoor as the authentication service provider. The remainder of this
  article will illustrate using this as an example. If you are using other login authentication
  services like Logto, the process should be similar, but be aware that port configurations may
  differ among different services.
</Callout>

In the following, it is assumed that in addition to the above services, you are also running an **Nginx** layer for reverse proxy and SSL configuration.

The domain and corresponding service port descriptions are as follows:

- `lobe.example.com`: This is your LobeChat service domain, which needs to reverse proxy to the LobeChat service port, default is `3210`.
- `auth.example.com`: This is your Logto UI domain, which needs to reverse proxy to the Logto WebUI service port, default is `8000`.
- `minio.example.com`: This is your MinIO API domain, which needs to reverse proxy to the MinIO API service port, default is `9000`.
- `minio-ui.example.com`: Optional, this is your MinIO UI domain, which needs to reverse proxy to the MinIO WebUI service port, default is `9001`.

#### Configuration File

```sh
bash <(curl -fsSL https://raw.githubusercontent.com/lobehub/lobe-chat/HEAD/docker-compose/local/setup.sh) -f -l zh_CN
docker compose up -d
```

Make sure to save the newly generated password at this time!

After running, you will get three files:

- init\_data.json
- docker-compose.yml
- .env

Next, modify the configuration files to achieve domain release.

1. Modify the `docker-compose.yml` file.

   1. Change the `MINIO_API_CORS_ALLOW_ORIGIN` field of `minio`.

   ```yaml
   'MINIO_API_CORS_ALLOW_ORIGIN=https://lobe.example.com'
   ```

   2. Modify the `origin` field of `casdoor`.

   ```yaml
   origin: 'https://auth.example.com'
   ```

   3. Modify the `environment` field of `lobe`.

   ```yaml
   # - 'APP_URL=http://localhost:3210'
   - 'APP_URL=https://lobe.example.com'

   - 'NEXT_AUTH_SSO_PROVIDERS=casdoor'
   - 'KEY_VAULTS_SECRET=Kix2wcUONd4CX51E/ZPAd36BqM4wzJgKjPtz2sGztqQ='
   - 'NEXT_AUTH_SECRET=NX2kaPE923dt6BL2U8e9oSre5RfoT7hg'
   # - 'AUTH_URL=http://localhost:${LOBE_PORT}/api/auth'
   - 'AUTH_URL=https://lobe.example.com/api/auth'

   # - 'AUTH_CASDOOR_ISSUER=http://localhost:${CASDOOR_PORT}'
   - 'AUTH_CASDOOR_ISSUER=https://auth.example.com'

   - 'DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD}@postgresql:5432/${LOBE_DB_NAME}'
   # - 'S3_ENDPOINT=http://localhost:${MINIO_PORT}'
   - 'S3_ENDPOINT=https://minio.example.com'

   - 'S3_BUCKET=${MINIO_LOBE_BUCKET}'
   # - 'S3_PUBLIC_DOMAIN=http://localhost:${MINIO_PORT}'
   - 'S3_PUBLIC_DOMAIN=https://minio.example.com'

   - 'S3_ENABLE_PATH_STYLE=1'
   - 'LLM_VISION_IMAGE_USE_BASE64=1'
   ```

2. Modify the `.env` file.

<Callout type="info">For security reasons, modify the ROOT USER field in the `.env` file.</Callout>

```sh
# MinIO S3 configuration
MINIO_ROOT_USER=XXXX
MINIO_ROOT_PASSWORD=XXXX
```

#### Postgres Database Configuration

You can check the logs with the following command:

```sh
docker logs -f lobe-chat
```

<Callout type="tip">
  In our official Docker images, automatic migration of the database schema is performed before
  starting the images. Our official images guarantee the stability of "empty database -> complete
  tables" for automatic table creation. Therefore, we recommend your database instance use an empty
  table instance to avoid the trouble of manually maintaining table structure or migrations.
</Callout>

If you encounter issues during table creation, you can try the following command to forcibly remove the database container and restart:

```sh
docker compose down  # Stop the service
sudo rm -rf ./data   # Remove mounted database data
docker compose up -d # Restart
```

#### Login Authentication Service Configuration

You first need to access the WebUI for configuration:

- If you have set up the reverse proxy as mentioned before, open `https://auth.example.com`
- Otherwise, after port mapping, open `http://localhost:8000`

Log in to the admin account:

- The default username is admin.
- The default password is the random password generated when downloading the config file. If forgotten, you can find it in the `init_data.json` file.

After logging in, perform the following actions:

1. In `User Management -> Organizations`, add a new organization with the name and display name `Lobe Users`. Keep the rest as default.
2. In `Authentication -> Apps`, add a new application.

- Name and display name should be `LobeChat`.
- Organization should be `Lobe Users`.
- Add a line in Redirect URLs as `https://lobe.example.com/api/auth/callback/casdoor`.
- Disable all login methods except password.
- Fill in the client ID and client secret in the `.env` file under `AUTH_CASDOOR_ID` and `AUTH_CASDOOR_SECRET`.
- (Optional) Design the appearance of the login and registration pages by mimicking the `built-in` application configuration.
- Save and exit.

<Callout type="info">
  Following the steps above ensures that not all users are administrators by default, leading to an
  unsafe situation.
</Callout>

#### S3 Object Storage Service Configuration

This article uses MinIO as an example to explain the configuration process. If you are using another S3 service provider, please refer to their documentation for configuration.

<Callout type="warning">
  Please remember to configure the corresponding S3 service provider's CORS settings to ensure that LobeChat can access the S3 service correctly.

  In this document, you need to allow cross-origin requests from `https://lobe.example.com`. This can either be configured in MinIO WebUI under `Configuration - API - Cors Allow Origin`, or in the Docker Compose configuration under `minio - environment - MINIO_API_CORS_ALLOW_ORIGIN`.

  If you use the second method (which is also the default), you will no longer be able to configure it in the MinIO WebUI.
</Callout>

You first need to access the WebUI for configuration:

- If you have set up the reverse proxy as mentioned before, open `https://minio-ui.example.com`
- Otherwise, after port mapping, open `http://localhost:9001`

1. Enter the `MINIO_ROOT_USER` and `MINIO_ROOT_PASSWORD` you set in the login interface, then click login.

2. In the left panel under User / Access Keys, click `Create New Access Key`, no additional modifications needed, and fill the generated `Access Key` and `Secret Key` into your `.env` file under `S3_ACCESS_KEY_ID` and `S3_SECRET_ACCESS_KEY`.

   <Image alt="Create MinIO Access Key" src="https://github.com/user-attachments/assets/72f02ce5-9991-425b-9864-9113ee1ed6bf" />

3. Restart the LobeChat service:

   ```sh
   docker compose up -d
   ```

At this point, you have successfully deployed the LobeChat database version, and you can access your LobeChat service at `https://lobe.example.com`.

#### Configuration Files

For convenience, here is a summary of example configuration files required for the production deployment using the Casdoor authentication scheme:

- `.env`

```sh
# Proxy, if you need it
# HTTP_PROXY=http://localhost:7890
# HTTPS_PROXY=http://localhost:7890

# Other environment variables, as needed. You can refer to the environment variables configuration for the client version, making sure not to have ACCESS_CODE.
# OPENAI_API_KEY=sk-xxxx
# OPENAI_PROXY_URL=https://api.openai.com/v1
# OPENAI_MODEL_LIST=...

# ===========================
# ====== Preset config ======
# ===========================
# if no special requirements, no need to change
LOBE_PORT=3210
CASDOOR_PORT=8000
MINIO_PORT=9000

# Postgres related, which are the necessary environment variables for DB
LOBE_DB_NAME=lobechat
POSTGRES_PASSWORD=uWNZugjBqixf8dxC

# Casdoor secret
AUTH_CASDOOR_ID=943e627d79d5dd8a22a1
AUTH_CASDOOR_SECRET=6ec24ac304e92e160ef0d0656ecd86de8cb563f1

# MinIO S3 configuration
MINIO_ROOT_USER=Joe
MINIO_ROOT_PASSWORD=Crj1570768

# Configure the bucket information of MinIO
MINIO_LOBE_BUCKET=lobe
S3_ACCESS_KEY_ID=dB6Uq9CYZPdWSZouPyEd
S3_SECRET_ACCESS_KEY=aPBW8CVULkh8bw1GatlT0GjLihcXHLNwRml4pieS
```

- `docker-compose.yml`

```yaml
name: lobe-chat-database
services:
  network-service:
    image: alpine
    container_name: lobe-network
    ports:
      - '${MINIO_PORT}:${MINIO_PORT}' # MinIO API
      - '9001:9001' # MinIO Console
      - '${CASDOOR_PORT}:${CASDOOR_PORT}' # Casdoor
      - '${LOBE_PORT}:3210' # LobeChat
    command: tail -f /dev/null
    networks:
      - lobe-network

  postgresql:
    image: pgvector/pgvector:pg16
    container_name: lobe-postgres
    ports:
      - '5432:5432'
    volumes:
      - './data:/var/lib/postgresql/data'
    environment:
      - 'POSTGRES_DB=${LOBE_DB_NAME}'
      - 'POSTGRES_PASSWORD=${POSTGRES_PASSWORD}'
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U postgres']
      interval: 5s
      timeout: 5s
      retries: 5
    restart: always
    networks:
      - lobe-network

  minio:
    image: minio/minio:RELEASE.2025-04-22T22-12-26Z
    container_name: lobe-minio
    network_mode: 'service:network-service'
    volumes:
      - './s3_data:/etc/minio/data'
    environment:
      - 'MINIO_ROOT_USER=${MINIO_ROOT_USER}'
      - 'MINIO_ROOT_PASSWORD=${MINIO_ROOT_PASSWORD}'
      # - 'MINIO_API_CORS_ALLOW_ORIGIN=http://localhost:${LOBE_PORT}'
      - 'MINIO_API_CORS_ALLOW_ORIGIN=https://lobe.example.com'
    restart: always
    command: >
      server /etc/minio/data --address ":${MINIO_PORT}" --console-address ":9001"


  casdoor:
    image: casbin/casdoor
    container_name: lobe-casdoor
    entrypoint: /bin/sh -c './server --createDatabase=true'
    network_mode: 'service:network-service'
    depends_on:
      postgresql:
        condition: service_healthy
    environment:
      RUNNING_IN_DOCKER: 'true'
      driverName: 'postgres'
      dataSourceName: 'user=postgres password=${POSTGRES_PASSWORD} host=postgresql port=5432 sslmode=disable dbname=casdoor'
      # origin: 'http://localhost:${CASDOOR_PORT}'
      origin: 'https://auth.example.com'
      runmode: 'dev'
    volumes:
      - ./init_data.json:/init_data.json

  lobe:
    image: lobehub/lobe-chat-database
    container_name: lobe-chat-database
    network_mode: 'service:network-service'
    depends_on:
      postgresql:
        condition: service_healthy
      network-service:
        condition: service_started
      minio:
        condition: service_started
      casdoor:
        condition: service_started

    environment:
      # - 'APP_URL=http://localhost:3210'
      - 'APP_URL=https://lobe.example.com'

      - 'NEXT_AUTH_SSO_PROVIDERS=casdoor'
      - 'KEY_VAULTS_SECRET=Kix2wcUONd4CX51E/ZPAd36BqM4wzJgKjPtz2sGztqQ='
      - 'NEXT_AUTH_SECRET=NX2kaPE923dt6BL2U8e9oSre5RfoT7hg'
      # - 'AUTH_URL=http://localhost:${LOBE_PORT}/api/auth'
      - 'AUTH_URL=https://lobe.example.com/api/auth'

      # - 'AUTH_CASDOOR_ISSUER=http://localhost:${CASDOOR_PORT}'
      - 'AUTH_CASDOOR_ISSUER=https://auth.example.com'

      - 'DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD}@postgresql:5432/${LOBE_DB_NAME}'
      # - 'S3_ENDPOINT=http://localhost:${MINIO_PORT}'
      - 'S3_ENDPOINT=https://minio.example.com'

      - 'S3_BUCKET=${MINIO_LOBE_BUCKET}'
      # - 'S3_PUBLIC_DOMAIN=http://localhost:${MINIO_PORT}'
      - 'S3_PUBLIC_DOMAIN=https://minio.example.com'

      - 'S3_ENABLE_PATH_STYLE=1'
      - 'LLM_VISION_IMAGE_USE_BASE64=1'
    env_file:
      - .env
    restart: always

volumes:
  data:
    driver: local
  s3_data:
    driver: local

networks:
  lobe-network:
    driver: bridge
```

[docker-pulls-link]: https://hub.docker.com/r/lobehub/lobe-chat-database
[docker-pulls-shield]: https://img.shields.io/docker/pulls/lobehub/lobe-chat-database?color=45cc11&labelColor=black&style=flat-square
[docker-release-link]: https://hub.docker.com/r/lobehub/lobe-chat-database
[docker-release-shield]: https://img.shields.io/docker/v/lobehub/lobe-chat-database?color=369eff&label=docker&labelColor=black&logo=docker&logoColor=white&style=flat-square&sort=semver
[docker-size-link]: https://hub.docker.com/r/lobehub/lobe-chat-database
[docker-size-shield]: https://img.shields.io/docker/image-size/lobehub/lobe-chat-database?color=369eff&labelColor=black&style=flat-square&sort=semver
