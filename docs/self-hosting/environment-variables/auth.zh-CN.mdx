---
title: LobeChat 身份验证服务设置
description: 了解如何配置 LobeChat 的身份验证服务环境变量。
tags:
  - LobeChat
  - 身份验证服务
  - 单点登录
  - Next Auth
  - Clerk
---

# 身份验证服务

LobeChat 在部署时提供了完善的身份验证服务能力，以下是相关的环境变量，你可以使用这些环境变量轻松定义需要在 LobeChat 中开启的身份验证服务。

## Next Auth

### 通用设置

#### `NEXT_PUBLIC_ENABLE_NEXT_AUTH`

- v1.52.0 之后有变更
- 针对使用 Vercel 部署中使用 next-auth 的用户，需要额外添加 NEXT\_PUBLIC\_ENABLE\_NEXT\_AUTH=1 环境变量来确保开启 Next Auth
- 针对使用自构建镜像中使用 clerk 的用户，需要额外配置 NEXT\_PUBLIC\_ENABLE\_NEXT\_AUTH=0 环境变量来关闭 Next Auth
- 其他标准部署场景（Vercel 中使用 Clerk 与 Docker 中使用 next-auth ）不受影响

#### `NEXT_AUTH_SECRET`

- 类型：必选
- 描述：用于加密 Auth.js 会话令牌的密钥。您可以使用以下命令生成秘钥： `openssl rand -base64 32`.
- 默认值: `-`
- 示例: `Tfhi2t2pelSMEA8eaV61KaqPNEndFFdMIxDaJnS1CUI=`

#### `NEXT_AUTH_SSO_PROVIDERS`

- 类型：可选
- 描述：选择 LoboChat 的单点登录提供商。如果有多个单点登录提供商，请用逗号分隔，例如 `auth0,microsoft-entra-id,authentik`
- 默认值: `auth0`
- 示例: `auth0,microsoft-entra-id,authentik`

#### `NEXTAUTH_URL`

- 类型：可选
- 描述：该 URL 用于指定 Auth.js 在执行 OAuth 验证时的回调地址，在 Vercel 上部署时无需设置。
- 默认值：`-`
- 示例：`https://example.com/api/auth`

### Auth0

#### `AUTH_AUTH0_ID`

- 类型：必选
- 描述: Auth0 应用程序的 Client ID，您可以访问[这里](https://manage.auth0.com/dashboard)并导航至应用程序设置来查看
- 默认值: `-`
- 示例: `evCnOJP1UX8FMnXR9Xkj5t0NyFn5p70P`

#### `AUTH_AUTH0_SECRET`

- 类型：必选
- 描述: Auth0 应用程序的 Client Secret
- 默认值: `-`
- 示例: `****************************************************************`

#### `AUTH_AUTH0_ISSUER`

- 类型：必选
- 描述: Auth0 应用程序的签发人 / 域
- 默认值: `-`
- 示例: `https://example.auth0.com`

### Authelia

#### `AUTH_AUTHELIA_ID`

- 类型：必选
- 描述: Authelia 提供程序的 Client ID
- 默认值: `-`
- 示例: `lobe-chat`

#### `AUTH_AUTHELIA_SECRET`

- 类型：必选
- 描述: Authelia 提供程序的 Client Secret 的明文
- 默认值: `-`
- 示例: `insecure_secret`

#### `AUTH_AUTHELIA_ISSUER`

- 类型：必选
- 描述: Authentik 提供程序的 OpenID Connect 颁发者
- 默认值: `-`
- 示例: `https://sso.example.com`

### Authentik

#### `AUTH_AUTHENTIK_ID`

- 类型：必选
- 描述: Authentik 提供程序的 Client ID
- 默认值: `-`
- 示例: `YNtbIRlYF8Kj66mTLue59nsGLlb7HNyx1qjPH6VS`

#### `AUTH_AUTHENTIK_SECRET`

- 类型：必选
- 描述: Authentik 提供程序的 Client Secret
- 默认值: `-`
- 示例: `********************************************************************************************************************************`

#### `AUTH_AUTHENTIK_ISSUER`

- 类型：必选
- 描述: Authentik 提供程序的 OpenID Connect 颁发者
- 默认值: `-`
- 示例: `https://your-authentik-domain.com/application/o/slug/`

### Casdoor

#### `AUTH_CASDOOR_ID`

- 类型：必选
- 描述: Casdoor 提供程序的 Client ID
- 默认值: `-`
- 示例: `570bfa85a21800a25198`

#### `AUTH_CASDOOR_SECRET`

- 类型：必选
- 描述: Casdoor 提供程序的 Client Secret 的明文
- 默认值: `-`
- 示例: `233a623a15eac2db2e43bb8a323eda729552c405`

#### `AUTH_CASDOOR_ISSUER`

- 类型：必选
- 描述: Casdoor 提供程序的 OpenID Connect 颁发者
- 默认值: `-`
- 示例: `https://lobe-auth-api.example.com/`

### Cloudflare Zero Trust

#### `AUTH_CLOUDFLARE_ZERO_TRUST_ID`

- 类型：必选
- 描述: Cloudflare Zero Trust 提供程序的 Client ID
- 默认值: `-`
- 示例: `711963a58df8c943cfd6c487cac99ce9f6ee0c88c0b7bf94584b8ff052fcb09c`

#### `AUTH_CLOUDFLARE_ZERO_TRUST_SECRET`

- 类型：必选
- 描述: Cloudflare Zero Trust 提供程序的 Client Secret 的明文
- 默认值: `-`
- 示例: `8f26d4ef834a828045b401e032ae128dbb00471bca53f0d25332323f525dfa30`

#### `AUTH_CLOUDFLARE_ZERO_TRUST_ISSUER`

- 类型：必选
- 描述: Cloudflare Zero Trust 提供程序的 OpenID Connect 颁发者
- 默认值: `-`
- 示例: `https://example.cloudflareaccess.com/cdn-cgi/access/sso/oidc/711963a58df8c943cfd6c487cac99ce9f6ee0c88c0b7bf94584b8ff052fcb09c`

### Github

#### `AUTH_GITHUB_ID`

- 类型：必选
- 描述: Github 应用的客户端 ID。您可以在[这里](https://github.com/settings/apps)访问，并导航到应用程序设置以查看。
- 默认值: `-`
- 示例: `abd94200333283550508`

#### `AUTH_GITHUB_SECRET`

- 类型：必选
- 描述: Github 应用的客户端密钥。
- 默认值: `-`
- 示例: `dd262976ac0931d947e104891586a053f3d3750b`

### Logto

#### `AUTH_LOGTO_ID`

- 类型：必选
- 描述：Logto 应用程序的 Client ID。您可以在根据部署模式，在私有部署的 Logto 控制台或 [Logto Cloud](http://cloud.logto.io/) 中找到。
- 默认值：`-`
- 示例：`123456789012345678@your-project`

#### `AUTH_LOGTO_SECRET`

- 类型：必选
- 描述：Logto 应用程序的 Client Secret。
- 默认值：`-`
- 示例：`9QF1n5ATzU7Z3mHp2Iw4gKX8kY6oR7uW1DnKcV3LqX2jF6iG3fBmJ1kV7nS5zE6A`

#### `AUTH_LOGTO_ISSUER`

- 类型：必选
- 描述：Logto 应用程序的 OpenID Connect 颁发者（issuer）。根据部署模式，您可以在私有部署的 Logto 控制台或 [Logto Cloud](http://cloud.logto.io/) 中找到。
- 默认值：`-`
- 示例：`https://lobe-auth-api.example.com/oidc`

### Microsoft Entra ID

#### `AUTH_AZURE_AD_ID`

- 类型：必选
- 描述：Microsoft Entra ID 应用程序的客户端 ID。
- 默认值：`-`
- 示例：`be8f6da1-58c3-4f16-ff1b-78f5148e10df`

#### `AUTH_AZURE_AD_SECRET`

- 类型：必选
- 描述：Microsoft Entra ID 应用程序的客户端密钥。
- 默认值：`-`
- 示例：`~gI8Q.pTiN1vwB6Gl.E1yFT1ojcXABkdACfJXaNj`

#### `AUTH_AZURE_AD_TENANT_ID`

- 类型：必选
- 描述：Microsoft Entra ID 应用程序的租户 ID。
- 默认值：`-`
- 示例：`c8ae2f36-edf6-4cda-96b9-d3e198a47cba`

### ZITADEL

#### `AUTH_ZITADEL_ID`

- 类型：必选
- 描述：ZITADEL 应用的 Client ID。您可以在 ZITADEL 控制台应用设置中找到 Client ID。
- 默认值：`-`
- 示例：`123456789012345678@your-project`

#### `AUTH_ZITADEL_SECRET`

- 类型：必选
- 描述：ZITADEL 应用的 Client Secret。
- 默认值：`-`
- 示例：`9QF1n5ATzU7Z3mHp2Iw4gKX8kY6oR7uW1DnKcV3LqX2jF6iG3fBmJ1kV7nS5zE6A`

#### `AUTH_ZITADEL_ISSUER`

- 类型：必选
- 描述：ZITADEL 应用的 OpenID Connect 颁发者（issuer），通常为 ZITADEL 实例的 URL。您可以在 ZITADEL 控制台应用设置中的 `URLs` 选项卡中找到 issuer。
- 默认值：`-`
- 示例：`https://your-instance-abc123.zitadel.cloud`

### Okta

#### `AUTH_OKTA_ID`

- 类型：必选
- 描述：Okta 应用程序的 Client ID。您可以在 Okta 控制台的应用程序设置中找到。
- 默认值：`-`
- 示例：`ac12c950f3ce48c8a45a`

#### `AUTH_OKTA_SECRET`

- 类型：必选
- 描述：Okta 应用程序的 Client Secret。您可以在 Okta 控制台的应用程序设置中找到。
- 默认值：`-`
- 示例：`ex1HqvSOOkC5INqo42grOSqNvHoD4p84em1yy5QU7v88IZlaWGywFjYkrkpkSopt`

#### `AUTH_OKTA_ISSUER`

- 类型：必选
- 描述：Okta 应用程序的 OpenID Connect 颁发者（issuer）。这是 Okta 实例的 URL—— 如果设置了品牌化，也可以是您的自定义域名。
- 默认值：`-`
- 示例：`https://your-instance.okta.com`

### Generic OIDC

#### `AUTH_GENERIC_OIDC_ID`

- 类型：必选
- 描述: Generic OIDC 提供程序的 Client ID
- 默认值: `-`
- 示例: `_client_id_for_lobe_chat_`

#### `AUTH_GENERIC_OIDC_SECRET`

- 类型：必选
- 描述: Generic OIDC 提供程序的 Client Secret 的明文
- 默认值: `-`
- 示例: `_client_secret_for_lobe_chat_`

#### `AUTH_GENERIC_OIDC_ISSUER`

- 类型：必选
- 描述: Generic OIDC 提供程序的 OpenID Connect 颁发者
- 默认值: `-`
- 示例: `https://sso.example.com`

<Callout>
  如果您需要使用其他身份验证服务提供商，可以提交
  [功能请求](https://github.com/lobehub/lobe-chat/issues/new/choose) 或 Pull Request。
</Callout>

## Clerk

### `NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY`

- 类型：必选
- 描述： Clerk 应用程序的 Publishable key。您可以在[这里](https://dashboard.clerk.com)访问，并导航到 API Keys 以查看。
- 默认值：`-`
- 示例： `pk_test_Zmxvd4luZy1wdW1hLTIyLmNsXXJrTmFjY291bnRzLmRldiQ` （测试环境） / `pk_live_Y2xlcdsubG9iZWh1Yi1cbmMuY24k` （生产环境）

### `CLERK_SECRET_KEY`

- 类型：必选
- 描述： Clerk 应用程序的 Secret key。您可以在[这里](https://dashboard.clerk.com)访问，并导航到 API Keys 以查看。
- 默认值：`-`
- 示例： `sk_test_513Ma0P7IAWM1XMv4waxZjRYRajWTaCfJLjpEO3SD2` （测试环境） / `**************************************************`（生产环境）
