---
title: LobeChat Model Service Providers - Environment Variables and Configuration
description: >-
  Learn about the environment variables and configuration settings for various model service providers like OpenAI, Google AI, AWS Bedrock, Ollama, Perplexity AI, Anthropic AI, Mistral AI, Groq AI, OpenRouter AI, and 01.AI.

tags:
  - Model Service Providers
  - Environment Variables
---

# Model Providers

When deploying LobeChat, a rich set of environment variables related to model service providers is provided, allowing you to easily define the model service providers to be enabled in LobeChat.

## OpenAI

### `ENABLED_OPENAI`

- Type：Optional
- Description：Enables OpenAI as a model provider by default, turns off the OpenAI service when set to `0`
- Default：`1`
- Example：`0`

### `OPENAI_API_KEY`

- Type: Required
- Description: This is the API key you applied for on the OpenAI account page, you can check it out [here](https://platform.openai.com/api-keys)
- Default: -
- Example: `sk-xxxxxx...xxxxxx`

### `OPENAI_PROXY_URL`

- Type: Optional
- Description: If you manually configure the OpenAI interface proxy, you can use this configuration item to override the default OpenAI API request base URL
- Default: `https://api.openai.com/v1`
- Example: `https://api.chatanywhere.cn` or `https://aihubmix.com/v1`

<Callout type={'warning'}>
  Please check the request suffix of your proxy service provider. Some proxy service providers may
  add `/v1` to the request suffix, while others may not. If you find that the AI returns an empty
  message during testing, try adding the `/v1` suffix and retry.
</Callout>

<Callout type={'info'}>
  Whether to fill in `/v1` is closely related to the model service provider. For example, the
  default address of openai is `api.openai.com/v1`. If your proxy forwards the `/v1` interface, you
  can simply fill in `proxy.com`. However, if the model service provider directly forwards the
  `api.openai.com` domain, then you need to add `/v1` to the URL yourself.
</Callout>

Related discussions:

- [Why is the return value blank after installing Docker, configuring environment variables?](https://github.com/lobehub/lobe-chat/discussions/623)
- [Reasons for errors when using third-party interfaces](https://github.com/lobehub/lobe-chat/discussions/734)
- [No response in chat after filling in the proxy server address](https://github.com/lobehub/lobe-chat/discussions/1065)

### `OPENAI_MODEL_LIST`

- Type: Optional
- Description: Used to control the model list, use `+` to add a model, use `-` to hide a model, use `model_name=display_name` to customize the display name of a model, separated by commas. Definition syntax rules see [model-list][model-list]
- Default: `-`
- Example: `+qwen-7b-chat,+glm-6b,-gpt-3.5-turbo,gpt-4-0125-preview=gpt-4-turbo`

The above example adds `qwen-7b-chat` and `glm-6b` to the model list, removes `gpt-3.5-turbo` from the list, and displays the name of `gpt-4-0125-preview` as `gpt-4-turbo`. If you want to disable all models first and then enable specific models, you can use `-all,+gpt-3.5-turbo`, which means only `gpt-3.5-turbo` will be enabled.

You can find all current model names in [modelProviders](https://github.com/lobehub/lobe-chat/tree/main/src/config/modelProviders).

## Azure OpenAI

If you need to use Azure OpenAI to provide model services, you can refer to the [Deploying with Azure OpenAI](/docs/self-hosting/examples/azure-openai) section for detailed steps. Here, we will list the environment variables related to Azure OpenAI.

### `AZURE_API_KEY`

- Type: Required
- Description: This is the API key you applied for on the Azure OpenAI account page
- Default: -
- Example: `c55168be3874490ef0565d9779ecd5a6`

### `AZURE_ENDPOINT`

- Type: Required
- Description: The endpoint of Azure OpenAI, you can find it in the Azure OpenAI account page
- Default: `-`
- Example: `https://docs-test-001.openai.azure.com`

### `AZURE_API_VERSION`

- Type: Required
- Description: The API version of Azure, following the format YYYY-MM-DD
- Default: `-`
- Example: `2023-02-01`, refer to [latest version](https://docs.microsoft.com/zh-cn/azure/developer/javascript/api-reference/es-modules/azure-sdk/ai-translation/translationconfiguration?view=azure-node-latest#api-version)

### `AZURE_MODEL_LIST`

- Type: Optional
- Description: Used to control the model list, use `+` to add a model, use `-` to hide a model, use `id->deploymentName=displayName` to customize the display name of a model, separated by commas. Definition syntax rules see [model-list][model-list]
- Default: `-`
- Example: `gpt-35-turbo->my-deploy=GPT 3.5 Turbo` 或 `gpt-4-turbo->my-gpt4=GPT 4 Turbo<128000:vision:fc>`

## Google AI

### `GOOGLE_API_KEY`

- Type: Required
- Description: This is the API key you applied for in the Google AI Platform to access Google AI services
- Default: -
- Example: `AIraDyDwcw254kwJaGjI9wwaHcdDCS__Vt3xQE`

### `GOOGLE_PROXY_URL`

- Type: Optional
- Description: If you manually configure the Google API proxy, you can use this configuration item to override the default Google API request base URL
- Default: `https://generativelanguage.googleapis.com`
- Example: `https://api.genai.gd.edu.kg/google`

### `GOOGLE_MODEL_LIST`

- Type: Optional
- Description: Used to control the model list, use `+` to add a model, use `-` to hide a model, use `model_name=display_name` to customize the display name of a model, separated by commas. Definition syntax rules see [model-list][model-list]
- Default: `-`
- Example: `-all,+gemini-1.5-flash-latest,+gemini-1.5-pro-latest`

## Anthropic AI

### `ANTHROPIC_API_KEY`

- Type: Required
- Description: This is the API key you applied from Anthropic AI
- Default: -
- Example: `sk-ant-apixx-xxxxxxxxx-xxxxxxxxxxxxxxxxx-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx-xxxxxxxx`

### `ANTHROPIC_PROXY_URL`

- Type: Optional
- Description: If you manually configure the Anthropic API proxy, you can use this configuration item to override the default Anthropic API request base URL
- Default: `https://api.anthropic.com`
- Example: `https://my-anthropic-proxy.com`

## AWS Bedrock

### `ENABLED_AWS_BEDROCK`

- Type：Optional
- Description：Enables AWS Bedrock as a model provider by default, turns on the AWS Bedrock service when set to `1`
- Default：`0`
- Example：`1`

### `AWS_ACCESS_KEY_ID`

- Type: Required
- Description: Access key ID for AWS service authentication
- Default: -
- Example: `********************`

### `AWS_SECRET_ACCESS_KEY`

- Type: Required
- Description: Key for AWS service authentication
- Default: -
- Example: `Th3vXxLYpuKcv2BARktPSTPxx+jbSiFT6/0w7oEC`

### `AWS_REGION`

- Type: Optional
- Description: Region setting for AWS services
- Default: `us-east-1`
- Example: `us-east-1`

## DeepSeek AI

### `DEEPSEEK_PROXY_URL`

- Type: Optional
- Description: If you manually configure the DeepSeek API proxy, you can use this configuration item to override the default DeepSeek API request base URL
- Default: `https://api.deepseek.com`
- Example: `https://my-deepseek-proxy.com`

### `DEEPSEEK_API_KEY`

- Type: Required
- Description: This is the API key you applied for in the DeepSeek AI service
- Default: -
- Example: `sk-xxxxxx...xxxxxx`

### `DEEPSEEK_MODEL_LIST`

- Type: Optional
- Description: Used to control the model list, use `+` to add a model, use `-` to hide a model, use `model_name=displayName` to customize the display name of a model, separated by commas. Definition syntax rules see [model-list][model-list]
- Default: `-`
- Example: `-all,+deepseek-reasoner`

## XAI

### `XAI_API_KEY`

- Type: Required
- Description: This is the API key you applied for in the XAI service
- Default: -
- Example: `xai-xxxxxx...xxxxxx`

## Wenxin

### `WENXIN_API_KEY`

- Type: Required
- Description: This is the API key you applied for in the Baidu Wenxin service
- Default: -
- Example: `xxxxxx...xxxxxx`

## OpenRouter AI

### `OPENROUTER_API_KEY`

- Type: Required
- Description: This is the API key you applied for in the OpenRouter AI service
- Default: -
- Example: `sk-or-v1-xxxxxx...xxxxxx=`

### `OPENROUTER_MODEL_LIST`

- Type: Optional
- Description: Used to specify a custom OpenRouter model list. Model definition syntax rules see [model-list][model-list]
- Default: `-`
- Example: `-all,+01-ai/yi-34b-chat,+huggingfaceh4/zephyr-7b-beta`

## PPIO

### `PPIO_API_KEY`

- Type: Required
- Description: This your PPIO API Key.
- Default: -
- Example: `sk_xxxxxxxxxx`

### `PPIO_MODEL_LIST`

- Type: Optional
- Description: Used to control the model list, use `+` to add a model, use `-` to hide a model, use `model_name=display_name` to customize the display name of a model, separated by commas. Definition syntax rules see [model-list][model-list]
- Default: `-`
- Example: `-all,+deepseek/deepseek-v3/community,+deepseek/deepseek-r1-distill-llama-70b`

## Github

### `GITHUB_TOKEN`

- Type: Required
- Description: This is your Personal access tokens you got for in the Github
- Default: `-`
- Example：`ghp_xxxxxx...xxxxxx=`

### `GITHUB_MODEL_LIST`

- Type: Optional
- Description: Used to control the model list, use `+` to add a model, use `-` to hide a model, use `model_name=display_name` to customize the display name of a model, separated by commas. Definition syntax rules see [model-list][model-list]
- Default: `-`
- Example: `-all,+gpt-4o,+gpt-4o-mini`

## TogetherAI

### `TOGETHERAI_API_KEY`

- Type: Required
- Description: This is the API key you applied for in the TogetherAI service
- Default: -
- Example: `xxxxxx...xxxxxx`

### `TOGETHERAI_MODEL_LIST`

- Type: Optional
- Description: Used to specify a custom TogetherAI model list. Model definition syntax rules see [model-list][model-list]
- Default: `-`
- Example: `-all,+meta-llama/Meta-Llama-3.1-8B-Instruct-Turbo,+meta-llama/Meta-Llama-3.1-70B-Instruct-Turbo`

## Fireworks AI

### `FIREWORKSAI_API_KEY`

- Type: Required
- Description: This is the API key you applied for in the Fireworks AI service
- Default: `-`
- Example：`xxxxxx...xxxxxx`

### `FIREWORKSAI_MODEL_LIST`

- Type: Optional
- Description: Used to control the model list, use `+` to add a model, use `-` to hide a model, use `model_name=display_name` to customize the display name of a model, separated by commas. Definition syntax rules see [model-list][model-list]
- Default: `-`
- Example: `-all,+accounts/fireworks/models/firefunction-v2,+accounts/fireworks/models/firefunction-v1`

## Ollama

### `ENABLED_OLLAMA`

- Type：Optional
- Description：Enables Ollama as a model provider by default, turns off the Ollama service when set to `0`
- Default：`1`
- Example：`0`

### `OLLAMA_PROXY_URL`

- Type: Required
- Description: Used to enable the Ollama service, setting this will display optional open-source language models in the language model list and can also specify custom language models
- Default: -
- Example: `http://127.0.0.1:11434`

### `OLLAMA_MODEL_LIST`

- Type: Optional
- Description: Used to specify a custom Ollama language model. Model definition syntax rules see [model-list][model-list]
- Default: -
- Example: `llama2:7B`

## Moonshot AI

### `MOONSHOT_API_KEY`

- Type: Required
- Description: This is the API key you applied for in the Moonshot AI service
- Default: -
- Example: `Y2xpdGhpMzNhZXNoYjVtdnZjMWc6bXNrLWIxQlk3aDNPaXpBWnc0V1RaMDhSRmRFVlpZUWY=`

### `MOONSHOT_PROXY_URL`

- Type: Optional
- Description: If you manually configure the Moonshot API proxy, you can use this configuration item to override the default Moonshot API request base URL
- Default: `https://api.moonshot.cn/v1`
- Example: `https://my-moonshot-proxy.com/v1`

## Perplexity AI

### `PERPLEXITY_API_KEY`

- Type: Required
- Description: This is the API key you applied from Perplexity AI
- Default: -
- Example: `pplx-xxxxxx...xxxxxx`

### `PERPLEXITY_PROXY_URL`

- Type: Optional
- Description: If you manually configure the Perplexity API proxy, you can use this configuration item to override the default Perplexity API request base URL
- Default: `https://api.Perplexity.ai`
- Example: `https://my-Perplexity-proxy.com`

### `PERPLEXITY_MODEL_LIST`

- Type: Optional
- Description: Used to control the model list, use `+` to add a model, use `-` to hide a model, use `model_name=display_name` to customize the display name of a model, separated by commas. Definition syntax rules see
- Default: `-`
- Example: `-all,+llama-3.1-sonar-small-128k-online,+llama-3.1-sonar-small-128k-chat`

## Minimax AI

### `MINIMAX_API_KEY`

- Type: Required
- Description: This is the API key you applied for in the Minimax AI service
- Default: -
- Example: `xxxxxx...xxxxxx`

## Mistral AI

### `MISTRAL_API_KEY`

- Type: Required
- Description: This is the API key you applied for in the Mistral AI service
- Default: -
- Example: `xxxxxx...xxxxxx=`

## Groq AI

### `GROQ_API_KEY`

- Type: Required
- Description: This is the API key you applied from Groq AI
- Default: -
- Example: `gsk_xxxxxx...xxxxxx`

### `GROQ_PROXY_URL`

- Type: Optional
- Description: If you manually configure the Groq API proxy, you can use this configuration item to override the default Groq API request base URL
- Default: `https://api.groq.com/openai/v1`
- Example: `https://my-groq-proxy.com/v1`

### `GROQ_MODEL_LIST`

- Type: Optional
- Description: Used to control the model list, use `+` to add a model, use `-` to hide a model, use `model_name=display_name` to customize the display name of a model, separated by commas. Definition syntax rules see [model-list][model-list]
- Default: `-`
- Example: `-all,+gemma2-9b-it,+llama-3.1-8b-instant`

## ZHIPU AI

### `ZHIPU_API_KEY`

- Type: Required
- Description: This is the API key you applied for in the ZHIPU AI service
- Default: -
- Example: `4582d332441a313f5c2ed9824d1798ca.rC8EcTAhgbOuAuVT`

### `ZHIPU_MODEL_LIST`

- Type: Optional
- Description: Used to control the model list, use `+` to add a model, use `-` to hide a model, use `model_name=display_name` to customize the display name of a model, separated by commas. Definition syntax rules see [model-list][model-list]
- Default: `-`
- Example: `-all,+glm-4-alltools,+glm-4-plus`

## 01.AI

### `ZEROONE_API_KEY`

- Type: Required
- Description: This is the API key you applied for in the 01.AI service
- Default: -
- Example：`xxxxxx...xxxxxx`

### `ZEROONE_MODEL_LIST`

- Type: Optional
- Description: Used to control the model list, use `+` to add a model, use `-` to hide a model, use `model_name=display_name` to customize the display name of a model, separated by commas. Definition syntax rules see [model-list][model-list]
- Default: `-`
- Example: `-all,+yi-large,+yi-large-rag`

## Qiniu

### `QINIU_API_KEY`

- Type: Required
- Description: This is the API key you can obtain from Qiniu AI service
- Default: -
- Example：`sk-xxxxx...xxxxx`

### `QINIU_MODEL_LIST`

- Type: Optional
- Description: Used to control the model list, use `+` to add a model, use `-` to hide a model, use `model_name=display_name` to customize the display name of a model, separated by commas. Definition syntax rules see [model-list][model-list]
- Default: `-`
- Example: `-all,+deepseek-r1,+deepseek-v3`

### `QINIU_PROXY_URL`

- Type: Optional
- Description: If you manually configure the Qiniu API proxy, you can use this configuration item to override the default Qiniu API request base URL
- Default: `https://api.qnaigc.com/v1`
- Example: `https://my-qnaigc.com/v1`

## Qwen

### `QWEN_API_KEY`

- Type: Required
- Description: This is the DashScope API key you can obtain from Alibaba Cloud
- Default: -
- Example：`sk-xxxxx...xxxxx`

### `QWEN_MODEL_LIST`

- Type: Optional
- Description: Used to control the model list, use `+` to add a model, use `-` to hide a model, use `model_name->deploymentName=display_name` to customize the display name of a model, separated by commas. The deploymentName `->deploymentName` can be omitted, and it defaults to the latest model version. Definition syntax rules see [model-list][model-list]
- Default: `-`
- Example: `-all,+qwen-turbo,+qwen-plus,+qwen-max->qwen-max-2025-01-25`

### `QWEN_PROXY_URL`

- Type: Optional
- Description: If you manually configure the Qwen API proxy, you can use this configuration item to override the default Qwen API request base URL
- Default: `https://dashscope.aliyuncs.com/compatible-mode/v1`
- Example: `https://my-qwen-proxy.com/v1`

## Stepfun AI

### `STEPFUN_API_KEY`

- Type: Required
- Description: This is the DashScope API key you can obtain from Stepfun AI service
- Default: -
- Example：`sk-xxxxx...xxxxx`

## Novita AI

### `NOVITA_API_KEY`

- Type: Required
- Description: This is the API key you applied for in the Novita AI service
- Default: -
- Example：`xxxxxx...xxxxxx`

### `NOVITA_MODEL_LIST`

- Type: Optional
- Description: Used to control the model list, use `+` to add a model, use `-` to hide a model, use `model_name=display_name` to customize the display name of a model, separated by commas. Definition syntax rules see [model-list][model-list]
- Default: `-`
- Example: `-all,+meta-llama/llama-3.1-8b-instruct,+meta-llama/llama-3.1-70b-instruct`

## BAICHUAN

### `BAICHUAN_API_KEY`

- Type: Required
- Description: This is the API key you applied for in the BAICHUAN service
- Default: -
- Example：`xxxxxx...xxxxxx`

## TAICHU

### `TAICHU_API_KEY`

- Type: Required
- Description: This is the API key you applied for in the TAICHU service
- Default: -
- Example：`xxxxxx...xxxxxx`

## 360 AI

### `AI360_API_KEY`

- Type: Required
- Description: This is the API key you applied for in the 360 AI service
- Default: -
- Example：`xxxxxx...xxxxxx`

## Siliconflow

### `SILICONCLOUD_API_KEY`

- Type: Required
- Description: This is the API key you applied from Siliconflow service
- Default: -
- Example: `xxxxxx...xxxxxx`

### `SILICONCLOUD_PROXY_URL`

- Type: Optional
- Description: If you manually configure the Siliconflow API proxy, you can use this configuration item to override the default Siliconflow API request base URL
- Default: `https://api.siliconflow.cn/v1`
- Example: `https://my-siliconflow-proxy.com/v1`

### `SILICONCLOUD_MODEL_LIST`

- Type: Optional
- Description: Used to control the model list, use `+` to add a model, use `-` to hide a model, use `model_name=display_name` to customize the display name of a model, separated by commas. Definition syntax rules see [model-list][model-list]
- Default: `-`
- Example: `-all,+deepseek-ai/DeepSeek-V2.5,+Qwen/Qwen2.5-7B-Instruct`

## Upstage AI

### `UPSTAGE_API_KEY`

- Type: Required
- Description: This is the API key you applied from Upstage AI service
- Default: -
- Example: `xxxxxx...xxxxxx`

## Spark AI

### `SPARK_API_KEY`

- Type: Required
- Description: This is the API key you applied from Spark AI service
- Default: -
- Example: `xxxxxx...xxxxxx`

## A21 AI

### `AI21_API_KEY`

- Type: Required
- Description: This is the API key you applied from `AI21_API_KEY` service
- Default: -
- Example: `xxxxxx...xxxxxx`

## HUNYUAN

### `HUNYUAN_API_KEY`

- Type: Required
- Description: This is the API key you applied from HUNYUAN service
- Default: -
- Example: `xxxxxx...xxxxxx`

### `HUNYUAN_MODEL_LIST`

- Type: Optional
- Description: Used to control the model list, use `+` to add a model, use `-` to hide a model, use `model_name=display_name` to customize the display name of a model, separated by commas. Definition syntax rules see [model-list][model-list]
- Default: `-`
- Example: `-all,+hunyuan-lite,+hunyuan-standard`

## Volcengine

### `VOLCENGINE_API_KEY`

- Type: Required
- Description: This is the API key you applied from Volcengine service, you can check it out [here](https://console.volcengine.com/ark/region:ark+cn-beijing/endpoint)
- Default: -
- Example: `ad925b3d-aaaa-bbbb-cccc-51fde2f50547`

### `VOLCENGINE_MODEL_LIST`

- Type: Optional
- Description: Used to control the model list, use `+` to add a model, use `-` to hide a model, use `model_name->deploymentName=display_name` to customize the display name of a model, separated by commas. The deploymentName `->deploymentName` can be omitted, and it defaults to the latest model version. Definition syntax rules see [model-list][model-list]
- Default: `-`
- Example: `-all,+deepseek-r1,+deepseek-v3->deepseek-v3-250324,+doubao-1.5-pro-256k,+doubao-1.5-pro-32k->doubao-1-5-pro-32k-250115,+doubao-1.5-lite-32k`

### `VOLCENGINE_PROXY_URL`

- Type: Optional
- Description: If you manually configure the Volcengine API proxy, you can use this configuration item to override the default Volcengine API request base URL
- Default: `https://ark.cn-beijing.volces.com/api/v3`
- Example: `https://my-volcengine-proxy.com/v1`

## InfiniAI

### `INFINIAI_API_KEY`

- Type: Required
- Description: This is the API key you applied from Infini-AI, you can check it out [here](https://cloud.infini-ai.com)
- Default: -
- Example: `sk-xxxxxx...xxxxxx`

### `INFINIAI_MODEL_LIST`

- Type: Optional
- Description: Used to control the model list, use `+` to add a model, use `-` to hide a model, use `model_name=display_name` to customize the display name of a model, separated by commas. Definition syntax rules see [model-list][model-list]
- Default: `-`
- Example: `-all,+qwq-32b,+deepseek-r1`

## FAL

### `ENABLED_FAL`

- Type: Optional
- Description: Enables FAL as a model provider by default. Set to `0` to disable the FAL service.
- Default: `1`
- Example: `0`

### `FAL_API_KEY`

- Type: Required
- Description: This is the API key you applied for in the FAL service.
- Default: -
- Example: `fal-xxxxxx...xxxxxx`

### `FAL_MODEL_LIST`

- Type: Optional
- Description: Used to control the FAL model list. Use `+` to add a model, `-` to hide a model, and `model_name=display_name` to customize the display name of a model. Separate multiple entries with commas. The definition syntax follows the same rules as other providers' model lists.
- Default: `-`
- Example: `-all,+fal-model-1,+fal-model-2=fal-special`

The above example disables all models first, then enables `fal-model-1` and `fal-model-2` (displayed as `fal-special`).

[model-list]: /docs/self-hosting/advanced/model-list
