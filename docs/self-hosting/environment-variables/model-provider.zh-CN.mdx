---
title: LobeChat 模型服务商相关环境变量配置指南
description: 了解如何配置各个模型服务商的环境变量以在 LobeChat 中使用不同的AI模型服务。
tags:
  - LobeChat
  - 模型服务商
  - 环境变量
  - AI模型
  - 配置指南
---

# 模型服务商

LobeChat 在部署时提供了丰富的模型服务商相关的环境变量，你可以使用这些环境变量轻松定义需要在 LobeChat 中开启的模型服务商。

## OpenAI

### `ENABLED_OPENAI`

- 类型：可选
- 描述：默认启用 OpenAI 作为模型供应商，当设为 0 时关闭 OpenAI 服务
- 默认值：`1`
- 示例：`0`

### `OPENAI_API_KEY`

- 类型：必选
- 描述：这是你在 OpenAI 账户页面申请的 API 密钥，可以前往 [这里](https://platform.openai.com/api-keys) 查看
- 默认值：-
- 示例：`sk-xxxxxx...xxxxxx`

### `OPENAI_PROXY_URL`

- 类型：可选
- 描述：如果你手动配置了 OpenAI 接口代理，可以使用此配置项来覆盖默认的 OpenAI API 请求基础 URL
- 默认值：`https://api.openai.com/v1`
- 示例：`https://api.chatanywhere.cn` 或 `https://aihubmix.com/v1`

<Callout type={'warning'}>
  请检查你的代理服务商的请求后缀，有的代理服务商会在请求后缀添加
  `/v1`，有的则不会。如果你在测试时发现 AI 返回的消息为空，请尝试添加 `/v1` 后缀后重试。
</Callout>

<Callout type={'info'}>
  是否填写 `/v1` 跟模型服务商有很大关系，比如 openai 的默认地址是 `api.openai.com/v1`
  。如果你的代理商转发了 `/v1` 这个接口，那么直接填 `proxy.com` 即可。 但如果模型服务商是直接转发了
  `api.openai.com` 域名，那么你就要自己加上 `/v1` 这个 url。
</Callout>

相关讨论：

- [Docker 安装，配置好环境变量后，为何返回值是空白？](https://github.com/lobehub/lobe-chat/discussions/623)
- [使用第三方接口报错的原因](https://github.com/lobehub/lobe-chat/discussions/734)
- [代理服务器地址填了聊天没任何反应](https://github.com/lobehub/lobe-chat/discussions/1065)

### `OPENAI_MODEL_LIST`

- 类型：可选
- 描述：用来控制模型列表，使用 `+` 增加一个模型，使用 `-` 来隐藏一个模型，使用 `模型名=展示名<扩展配置>` 来自定义模型的展示名，用英文逗号隔开。模型定义语法规则见 [模型列表][model-list]
- 默认值：`-`
- 示例：`+qwen-7b-chat,+glm-6b,-gpt-3.5-turbo,gpt-4-0125-preview=gpt-4-turbo`

上面示例表示增加 `qwen-7b-chat` 和 `glm-6b` 到模型列表，而从列表中删除 `gpt-3.5-turbo`，并将 `gpt-4-0125-preview` 模型名字展示为 `gpt-4-turbo`。如果你想先禁用所有模型，再启用指定模型，可以使用 `-all,+gpt-3.5-turbo`，则表示仅启用 `gpt-3.5-turbo`。

你可以在 [modelProviders](https://github.com/lobehub/lobe-chat/tree/main/src/config/modelProviders) 查找到当前的所有模型名。

## Azure OpenAI

如果你需要使用 Azure OpenAI 来提供模型服务，可以查阅 [使用 Azure OpenAI 部署](/zh/docs/self-hosting/examples/azure-openai) 章节查看详细步骤，这里将列举和 Azure OpenAI 相关的环境变量。

### `AZURE_API_KEY`

- 类型：必选
- 描述：这是你在 Azure OpenAI 账户页面申请的 API 密钥
- 默认值：-
- 示例：`c55168be3874490ef0565d9779ecd5a6`

### `AZURE_ENDPOINT`

- 类型：必选
- 描述：Azure API 地址，从 Azure 门户检查资源时，可在 “密钥和终结点” 部分中找到此值
- 默认值：`-`
- 示例：`https://docs-test-001.openai.azure.com`

### `AZURE_API_VERSION`

- 类型：必选
- 描述：Azure 的 API 版本，遵循 YYYY-MM-DD 格式
- 默认值：`-`
- 示例：`2024-02-01`，查阅 [最新版本](https://docs.microsoft.com/zh-cn/azure/developer/javascript/api-reference/es-modules/azure-sdk/ai-translation/translationconfiguration?view=azure-node-latest#api-version)

### `AZURE_MODEL_LIST`

- 类型：必选
- 描述：用来控制模型列表，使用 `模型名->部署名=展示名` 来自定义模型的展示名，用英文逗号隔开。支持扩展能力，其余语法规则详见 [模型列表][model-list]
- 默认值：`-`
- 示例：`gpt-35-turbo->my-deploy=GPT 3.5 Turbo` 或 `gpt-4-turbo->my-gpt4=GPT 4 Turbo<128000:vision:fc>`

## Google AI

### `GOOGLE_API_KEY`

- 类型：必选
- 描述：这是你在 Google AI Platform 申请的 API 密钥，用于访问 Google AI 服务
- 默认值：-
- 示例：`AIraDyDwcw254kwJaGjI9wwaHcdDCS__Vt3xQE`

### `GOOGLE_PROXY_URL`

- 类型：可选
- 描述：如果你手动配置了 Google 接口代理，可以使用此配置项来覆盖默认的 Google API 请求基础 URL
- 默认值：`https://generativelanguage.googleapis.com`
- 示例：`https://api.genai.gd.edu.kg/google`

### `GOOGLE_MODEL_LIST`

- 类型：可选
- 描述：用来控制模型列表，使用 `+` 增加一个模型，使用 `-` 来隐藏一个模型，使用 `模型名=展示名<扩展配置>` 来自定义模型的展示名，用英文逗号隔开。模型定义语法规则见 [模型列表][model-list]
- 默认值：`-`
- 示例：`-all,+gemini-1.5-flash-latest,+gemini-1.5-pro-latest`

## Anthropic AI

### `ANTHROPIC_API_KEY`

- 类型：必选
- 描述：这是你在 Anthropic AI 服务中申请的 API 密钥
- 默认值：-
- 示例：`sk-ant-apixx-xxxxxxxxx-xxxxxxxxxxxxxxxxx-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx-xxxxxxxx`

### `ANTHROPIC_PROXY_URL`

- 类型：可选
- 描述：如果你手动配置了 Anthropic 接口代理，可以使用此配置项来覆盖默认的 Anthropic API 请求基础 URL
- 默认值：`https://api.anthropic.com`
- 示例：`https://my-anthropic-proxy.com`

## AWS Bedrock

### `ENABLED_AWS_BEDROCK`

- 类型：可选
- 描述：当设为 1 时启用 AWS Bedrock 服务
- 默认值：`0`
- 示例：`1`

### `AWS_ACCESS_KEY_ID`

- 类型：必选
- 描述：用于 AWS 服务认证的访问键 ID
- 默认值：-
- 示例：`********************`

### `AWS_SECRET_ACCESS_KEY`

- 类型：必选
- 描述：用于 AWS 服务认证的密钥
- 默认值：-
- 示例：`Th3vXxLYpuKcv2BARktPSTPxx+jbSiFT6/0w7oEC`

### `AWS_REGION`

- 类型：可选
- 描述：AWS 服务的区域设置
- 默认值：`us-east-1`
- 示例：`us-east-1`

## DeepSeek AI

### `DEEPSEEK_PROXY_URL`

- 类型：可选
- 描述：如果您手动配置了 DeepSeek API 代理，可以使用此配置项覆盖默认的 DeepSeek API 请求基础 URL
- 默认值：`https://api.deepseek.com`
- 示例：`https://my-deepseek-proxy.com`

### `DEEPSEEK_API_KEY`

- 类型：必选
- 描述：这是你在 DeepSeek AI 服务中申请的 API 密钥
- 默认值：-
- 示例：`sk-xxxxxx...xxxxxx`

### `DEEPSEEK_MODEL_LIST`

- 类型：可选
- 描述：用来控制模型列表，使用 `+` 增加一个模型，使用 `-` 来隐藏一个模型，使用 `模型名=展示名<扩展配置>` 来自定义模型的展示名，用英文逗号隔开。模型定义语法规则见 [模型列表][model-list]
- 默认值：`-`
- 示例：`-all,+deepseek-reasoner`

## XAI

### `XAI_API_KEY`

- 类型：必选
- 描述：这是你在 XAI 服务中申请的 API 密钥
- 默认值：-
- 示例：`xai-xxxxxx...xxxxxx`

## 文心一言

### `WENXIN_API_KEY`

- 类型：必选
- 描述：这是你在百度智能云平台申请的文心一言 API 密钥
- 默认值：-
- 示例：`xxxxxx...xxxxxx`

## OpenRouter AI

### `OPENROUTER_API_KEY`

- 类型：必选
- 描述：这是你在 OpenRouter AI 服务中申请的 API 密钥
- 默认值：-
- 示例：`sk-or-v1-xxxxxx...xxxxxx=`

### `OPENROUTER_MODEL_LIST`

- 类型：可选
- 描述：用来控制模型列表，使用 `+` 增加一个模型，使用 `-` 来隐藏一个模型，使用 `模型名=展示名<扩展配置>` 来自定义模型的展示名，用英文逗号隔开。模型定义语法规则见 [模型列表][model-list]
- 默认值：`-`
- 示例：`-all,+01-ai/yi-34b-chat,+huggingfaceh4/zephyr-7b-beta`

## PPIO

### `PPIO_API_KEY`

- 类型：必选
- 描述：这是你在 PPIO 网站申请的 API 密钥
- 默认值：-
- 示例：`sk_xxxxxxxxxxxx`

### `PPIO_MODEL_LIST`

- 类型：可选
- 描述：用来控制模型列表，使用 `+` 增加一个模型，使用 `-` 来隐藏一个模型，使用 `模型名=展示名<扩展配置>` 来自定义模型的展示名，用英文逗号隔开。模型定义语法规则见 [模型列表][model-list]
- 默认值：`-`
- 示例：`-all,+deepseek/deepseek-v3/community,+deepseek/deepseek-r1-distill-llama-70b`

## Github

### `GITHUB_TOKEN`

- 类型：必选
- 描述：这是你在 Github 申请的 Personal access tokens
- 默认值：-
- 示例：`ghp_xxxxxx...xxxxxx=`

### `GITHUB_MODEL_LIST`

- 类型：可选
- 描述：用来控制模型列表，使用 `+` 增加一个模型，使用 `-` 来隐藏一个模型，使用 `模型名=展示名<扩展配置>` 来自定义模型的展示名，用英文逗号隔开。模型定义语法规则见 [模型列表][model-list]
- 默认值：`-`
- 示例：`-all,+gpt-4o,+gpt-4o-mini`

## TogetherAI

### `TOGETHERAI_API_KEY`

- 类型：必选
- 描述：这是你在 TogetherAI 服务中申请的 API 密钥
- 默认值：-
- 示例：`xxxxxx...xxxxxx`

### `TOGETHERAI_MODEL_LIST`

- 类型：可选
- 描述：用来控制模型列表，使用 `+` 增加一个模型，使用 `-` 来隐藏一个模型，使用 `模型名=展示名<扩展配置>` 来自定义模型的展示名，用英文逗号隔开。模型定义语法规则见 [模型列表][model-list]
- 默认值：`-`
- 示例：`-all,+meta-llama/Meta-Llama-3.1-8B-Instruct-Turbo,+meta-llama/Meta-Llama-3.1-70B-Instruct-Turbo`

## Fireworks AI

### `FIREWORKSAI_API_KEY`

- 类型：必选
- 描述：这是你在 Fireworks AI 服务中申请的 API 密钥
- 默认值：-
- 示例：`xxxxxx...xxxxxx`

### `FIREWORKSAI_MODEL_LIST`

- 类型：可选
- 描述：用来控制模型列表，使用 `+` 增加一个模型，使用 `-` 来隐藏一个模型，使用 `模型名=展示名<扩展配置>` 来自定义模型的展示名，用英文逗号隔开。模型定义语法规则见 [模型列表][model-list]
- 默认值：`-`
- 示例：`-all,+accounts/fireworks/models/firefunction-v2,+accounts/fireworks/models/firefunction-v1`

## Ollama

### `ENABLED_OLLAMA`

- 类型：可选
- 描述：默认启用 Ollama 作为模型供应商，当设为 0 时关闭 Ollama 服务
- 默认值：`1`
- 示例：`0`

### `OLLAMA_PROXY_URL`

- 类型：必选
- 描述：用于启用 Ollama 服务，设置后可在语言模型列表内展示可选开源语言模型，也可以指定自定义语言模型
- 默认值：-
- 示例：`http://127.0.0.1:11434`

### `OLLAMA_MODEL_LIST`

- 类型：可选
- 描述：用于指定自定义 Ollama 语言模型。模型定义语法规则见 [模型列表][model-list]
- 默认值：-
- 示例：`qwen:32B`

## Moonshot AI

### `MOONSHOT_API_KEY`

- 类型：必选
- 描述：这是你在 Moonshot AI 服务中申请的 API 密钥
- 默认值：-
- 示例：`Y2xpdGhpMzNhZXNoYjVtdnZjMWc6bXNrLWIxQlk3aDNPaXpBWnc0V1RaMDhSRmRFVlpZUWY=`

### `MOONSHOT_PROXY_URL`

- 类型：可选
- 描述：如果你手动配置了 Moonshot 接口代理，可以使用此配置项来覆盖默认的 Moonshot API 请求基础 URL
- 默认值：`https://api.moonshot.cn/v1`
- 示例：`https://my-moonshot-proxy.com/v1`

## Perplexity AI

### `PERPLEXITY_API_KEY`

- 类型：必选
- 描述：这是你在 Perplexity AI 服务中申请的 API 密钥
- 默认值：-
- 示例：`pplx-xxxxxx...xxxxxx`

### `PERPLEXITY_MODEL_LIST`

- 类型：可选
- 描述：用于指定自定义 Perplexity 语言模型。模型定义语法规则见 [模型列表][model-list]
- 默认值：-
- 示例： `-all,+llama-3.1-sonar-small-128k-online,+llama-3.1-sonar-small-128k-chat`

### `PERPLEXITY_PROXY_URL`

- 类型：可选
- 描述：如果你手动配置了 Perplexity 接口代理，可以使用此配置项来覆盖默认的 Perplexity API 请求基础 URL
- 默认值：`https://api.Perplexity.ai`
- 示例：`https://my-Perplexity-proxy.com`

## Minimax AI

### `MINIMAX_API_KEY`

- 类型：必选
- 描述：这是你在 Minimax AI 服务中申请的 API 密钥
- 默认值：-
- 示例：`xxxxxx...xxxxxx`

## Mistral AI

### `MISTRAL_API_KEY`

- 类型：必选
- 描述：这是你在 Mistral AI 服务中申请的 API 密钥
- 默认值：-
- 示例：`xxxxxx...xxxxxx=`

## Groq AI

### `GROQ_API_KEY`

- 类型：必选
- 描述：这是你在 Groq AI 服务中申请的 API 密钥
- 默认值：-
- 示例：`gsk_xxxxxx...xxxxxx`

### `GROQ_MODEL_LIST`

- 类型：可选
- 描述：用来控制模型列表，使用 `+` 增加一个模型，使用 `-` 来隐藏一个模型，使用 `模型名=展示名<扩展配置>` 来自定义模型的展示名，用英文逗号隔开。模型定义语法规则见 [模型列表][model-list]
- 默认值：`-`
- 示例：`-all,+gemma2-9b-it,+llama-3.1-8b-instant`

### `GROQ_PROXY_URL`

- 类型：可选
- 描述：如果你手动配置了 Groq 接口代理，可以使用此配置项来覆盖默认的 Groq API 请求基础 URL
- 默认值：`https://api.groq.com/openai/v1`
- 示例：`https://my-groq-proxy.com/v1`

## 智谱 AI

### `ZHIPU_API_KEY`

- 类型：必选
- 描述：这是你在 智谱 AI 服务中申请的 API 密钥
- 默认值：-
- 示例：`4582d332441a313f5c2ed9824d1798ca.rC8EcTAhgbOuAuVT`

### `ZHIPU_MODEL_LIST`

- 类型：可选
- 描述：用来控制模型列表，使用 `+` 增加一个模型，使用 `-` 来隐藏一个模型，使用 `模型名=展示名<扩展配置>` 来自定义模型的展示名，用英文逗号隔开。模型定义语法规则见 [模型列表][model-list]
- 默认值：`-`
- 示例：`-all,+glm-4-alltools,+glm-4-plus`

## 01 AI

### `ZEROONE_API_KEY`

- 类型：必选
- 描述：这是你在零一万物服务中申请的 API 密钥
- 默认值：-
- 示例：`xxxxxx...xxxxxx`

### `ZEROONE_MODEL_LIST`

- 类型：可选
- 描述：用来控制模型列表，使用 `+` 增加一个模型，使用 `-` 来隐藏一个模型，使用 `模型名=展示名<扩展配置>` 来自定义模型的展示名，用英文逗号隔开。模型定义语法规则见 [模型列表][model-list]
- 默认值：`-`
- 示例：`-all,+yi-large,+yi-large-rag`

## 七牛云

### `QINIU_API_KEY`

- 类型：必选
- 描述：这是你在七牛云上获取的 API 密钥
- 默认值：-
- 示例：`sk-xxxxx...xxxxx`

### `QINIU_MODEL_LIST`

- 类型：可选
- 描述：用来控制模型列表，使用 `+` 增加一个模型，使用 `-` 来隐藏一个模型，使用 `模型名=展示名<扩展配置>` 来自定义模型的展示名，用英文逗号隔开。模型定义语法规则见 [模型列表][model-list]
- 默认值：`-`
- 示例：`-all,+deepseek-r1,+deepseek-v3`

### `QINIU_PROXY_URL`

- 类型：可选
- 描述：如果你手动配置了 Qiniu 接口代理，可以使用此配置项来覆盖默认的 Qiniu API 请求基础 URL
- 默认值：`https://api.qnaigc.com/v1`
- 示例：`https://my-qnaigc.com/v1`

## 通义千问

### `QWEN_API_KEY`

- 类型：必选
- 描述：这是你在阿里云百炼平台上获取的 DashScope API 密钥
- 默认值：-
- 示例：`sk-xxxxx...xxxxx`

### `QWEN_MODEL_LIST`

- 类型：可选
- 描述：用来控制模型列表，使用 `+` 增加一个模型，使用 `-` 来隐藏一个模型，使用 `模型名->部署名=展示名<扩展配置>` 来自定义模型的展示名，用英文逗号隔开。部署名`->部署名`可以省略，默认为最新版本的模型。模型定义语法规则见 [模型列表][model-list]
- 默认值：`-`
- 示例：`-all,+qwen-turbo,+qwen-plus,+qwen-max->qwen-max-2025-01-25`

### `QWEN_PROXY_URL`

- 类型：可选
- 描述：如果你手动配置了 Qwen 接口代理，可以使用此配置项来覆盖默认的 Qwen API 请求基础 URL
- 默认值：`https://dashscope.aliyuncs.com/compatible-mode/v1`
- 示例：`https://my-qwen-proxy.com/v1`

## Stepfun AI

### `STEPFUN_API_KEY`

- 类型：必选
- 描述：这是你在 Stepfun AI 服务中申请的 API 密钥
- 默认值：-
- 示例：`xxxxxx...xxxxxx`

## Novita AI

### `NOVITA_API_KEY`

- 类型：必选
- 描述：这是你在 Novita AI 服务中申请的 API 密钥
- 默认值：-
- 示例：`xxxxxx...xxxxxx`

### `NOVITA_MODEL_LIST`

- 类型：可选
- 描述：用来控制模型列表，使用 `+` 增加一个模型，使用 `-` 来隐藏一个模型，使用 `模型名=展示名<扩展配置>` 来自定义模型的展示名，用英文逗号隔开。模型定义语法规则见 [模型列表][model-list]
- 默认值：`-`
- 示例：`-all,+meta-llama/llama-3.1-8b-instruct,+meta-llama/llama-3.1-70b-instruct`

## 百川

### `BAICHUAN_API_KEY`

- 类型：必选
- 描述：这是你在 百川智能 服务平台申请的 API 密钥
- 默认值：-
- 示例：`xxxxxx...xxxxxx`

## 紫东太初

### `TAICHU_API_KEY`

- 类型：必选
- 描述：这是你在 紫东太初 服务平台申请的 API 密钥
- 默认值：-
- 示例：`xxxxxx...xxxxxx`

## 360 AI

### `AI360_API_KEY`

- 类型：必选
- 描述：这是你在 360 智脑 服务平台申请的 API 密钥
- 默认值：-
- 示例：`xxxxxx...xxxxxx`

## Siliconflow

### `SILICONCLOUD_API_KEY`

- 类型：必选
- 描述：这是你在 Siliconflow 服务中申请的 API 密钥
- 默认值：-
- 示例：`xxxxxx...xxxxxx`

### `SILICONCLOUD_MODEL_LIST`

- 类型：可选
- 描述：用来控制模型列表，使用 `+` 增加一个模型，使用 `-` 来隐藏一个模型，使用 `模型名=展示名<扩展配置>` 来自定义模型的展示名，用英文逗号隔开。模型定义语法规则见 [模型列表][model-list]
- 默认值：`-`
- 示例：`-all,+deepseek-ai/DeepSeek-V2.5,+Qwen/Qwen2.5-7B-Instruct`

### `SILICONCLOUD_PROXY_URL`

- 类型：可选
- 描述：如果你手动配置了 Siliconflow 接口代理，可以使用此配置项来覆盖默认的 Siliconflow API 请求基础 URL
- 默认值：`https://api.siliconflow.cn/v1`
- 示例：`https://my-siliconflow-proxy.com/v1`

## Upstage AI

### `UPSTAGE_API_KEY`

- 类型：必选
- 描述：这是你在 Upstage AI 服务中申请的 API 密钥
- 默认值：-
- 示例：`xxxxxx...xxxxxx`

## Spark AI

### `SPARK_API_KEY`

- 类型：必选
- 描述：这是你在 Spark AI 服务中申请的 API 密钥
- 默认值：-
- 示例：`xxxxxx...xxxxxx`

## A21 AI

### `AI21_API_KEY`

- 类型：必选
- 描述：这是你在 A21 AI 服务中申请的 API 密钥
- 默认值：-
- 示例：`xxxxxx...xxxxxx`

## 腾讯混元

### `HUNYUAN_API_KEY`

- 类型：必选
- 描述：这是你在 腾讯混元 服务中申请的 API 密钥
- 默认值：-
- 示例：`xxxxxx...xxxxxx`

### `HUNYUAN_MODEL_LIST`

- 类型：可选
- 描述：用来控制模型列表，使用 `+` 增加一个模型，使用 `-` 来隐藏一个模型，使用 `模型名=展示名<扩展配置>` 来自定义模型的展示名，用英文逗号隔开。模型定义语法规则见 [模型列表][model-list]
- 默认值：`-`
- 示例：`-all,+hunyuan-lite,+hunyuan-standard`

## 火山引擎

### `VOLCENGINE_API_KEY`

- 类型：必选
- 描述：这是你在 火山引擎 服务中申请的 API 密钥，可以前往 [这里](https://console.volcengine.com/ark/region:ark+cn-beijing/endpoint) 查看
- 默认值：-
- 示例：`ad925b3d-aaaa-bbbb-cccc-51fde2f50547`

### `VOLCENGINE_MODEL_LIST`

- 类型：可选
- 描述：用来控制模型列表，使用 `+` 增加一个模型，使用 `-` 来隐藏一个模型，使用 `模型名->部署名=展示名<扩展配置>` 来自定义模型的展示名，用英文逗号隔开。部署名`->部署名`可以省略，默认为最新版本的模型。模型定义语法规则见 [模型列表][model-list]
- 默认值：`-`
- 示例：`-all,+deepseek-r1,+deepseek-v3->deepseek-v3-250324,+doubao-1.5-pro-256k,+doubao-1.5-pro-32k->doubao-1-5-pro-32k-250115,+doubao-1.5-lite-32k`

### `VOLCENGINE_PROXY_URL`

- 类型：可选
- 描述：如果你手动配置了 Volcengine 接口代理，可以使用此配置项来覆盖默认的 Volcengine API 请求基础 URL
- 默认值：`https://ark.cn-beijing.volces.com/api/v3`
- 示例：`https://my-volcengine-proxy.com/v1`

## InfiniAI

### `INFINIAI_API_KEY`

- 类型：必选
- 描述：这是你在 [Infini-AI](https://cloud.infini-ai.com) 申请的 API 密钥。
- 默认值：-
- 示例：`sk-xxxxxx...xxxxxx`

### `INFINIAI_MODEL_LIST`

- 类型：可选
- 描述：用来控制模型列表，使用 `+` 增加一个模型，使用 `-` 来隐藏一个模型，使用 `模型名=展示名<扩展配置>` 来自定义模型的展示名，用英文逗号隔开。模型定义语法规则见 [模型列表][model-list]
- 默认值：`-`
- 示例：`-all,+qwq-32b,+deepseek-r1`

## FAL

### `ENABLED_FAL`

- 类型：可选
- 描述：默认启用 FAL 作为模型供应商，当设为 0 时关闭 FAL 服务
- 默认值：`1`
- 示例：`0`

### `FAL_API_KEY`

- 类型：必选
- 描述：这是你在 FAL 服务中申请的 API 密钥
- 默认值：-
- 示例：`fal-xxxxxx...xxxxxx`

### `FAL_MODEL_LIST`

- 类型：可选
- 描述：用来控制 FAL 模型列表，使用 `+` 增加一个模型，使用 `-` 来隐藏一个模型，使用 `模型名=展示名` 来自定义模型的展示名，用英文逗号隔开。模型定义语法规则与其他 provider 保持一致。
- 默认值：`-`
- 示例：`-all,+fal-model-1,+fal-model-2=fal-special`

上述示例表示先禁用所有模型，再启用 `fal-model-1` 和 `fal-model-2`（显示名为 `fal-special`）。

[model-list]: /zh/docs/self-hosting/advanced/model-list
